package cn.need.cloud.biz.model.query.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/***
 * 产品树查询条件
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@Accessors(chain = true)
public class ProductTreeQuery {


    @Schema(description = "产品id")
    private Long productId;

    @Schema(description = "产品版本")
    private Integer productVersionInt;

    @Schema(description = "Prep工单类型")
    private String prepWorkOrderType;
}
