package cn.need.cloud.biz.model.bo.otc.pickingslip;

import cn.need.cloud.biz.model.bo.otc.workorder.OtcPrepWorkorderSplitDetailBO;
import cn.need.cloud.biz.model.entity.otc.OtcPrepPickingSlipDetail;
import lombok.Data;

/**
 * PrepDetail
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Data
public class OtcPrepPickingSlipSplitDetailBO {

    /**
     * 原单
     */
    private OtcPrepPickingSlipDetail prepDetail;

    /**
     * 拆单
     */
    private OtcPrepPickingSlipDetail splitPrepDetail;

    /**
     * 拆单数量
     */
    private Integer splitQty;

    /**
     * 工单详情拆单信息
     */
    private OtcPrepWorkorderSplitDetailBO splitWorkorderDetailHolder;

}
