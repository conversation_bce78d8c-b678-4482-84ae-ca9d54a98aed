package cn.need.cloud.biz.model.vo.product.page;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 产品 create对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "产品 vo对象")
public class ProductPageVO implements Serializable {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 交易伙伴ID
     */
    @Schema(description = "交易伙伴ID")
    private Long transactionPartnerId;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 供应商SKU
     */
    @Schema(description = "供应商SKU")
    private String supplierSku;

    /**
     * UPC码
     */
    @Schema(description = "UPC码")
    private String upc;

    /**
     * 组装产品标志
     */
    @Schema(description = "组装产品标志")
    private Boolean assemblyProductFlag;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 标题
     */
    @Schema(description = "标题")
    private String title;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 多箱标志
     */
    @Schema(description = "多箱标志")
    private Boolean multiboxFlag;

    /**
     * 组类型
     */
    @Schema(description = "组类型")
    private String groupType;

}