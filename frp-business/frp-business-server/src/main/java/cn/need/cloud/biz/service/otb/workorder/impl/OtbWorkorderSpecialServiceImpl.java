package cn.need.cloud.biz.service.otb.workorder.impl;

import cn.need.cloud.biz.client.constant.ErrorConstant;
import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.base.ProcessType;
import cn.need.cloud.biz.client.constant.enums.inventory.InventoryLockedStatusEnum;
import cn.need.cloud.biz.client.constant.enums.inventory.WorkOrderPrepStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otb.OtbWorkorderEnum;
import cn.need.cloud.biz.client.constant.special.RollbackConstant;
import cn.need.cloud.biz.model.bo.base.AllocationBO;
import cn.need.cloud.biz.model.bo.base.ChangeQtyLogBO;
import cn.need.cloud.biz.model.bo.base.WorkorderProcessBO;
import cn.need.cloud.biz.model.bo.otb.OtbPackageRollbackSingleWorkorderBO;
import cn.need.cloud.biz.model.bo.otb.putawayslip.OtbPutawaySlipPutAwayBO;
import cn.need.cloud.biz.model.bo.otb.putawayslip.OtbPutawaySlipPutAwayDetailBO;
import cn.need.cloud.biz.model.bo.otb.workorder.OtbWorkorderSplitBO;
import cn.need.cloud.biz.model.bo.otb.workorder.OtbWorkorderSplitDetailBO;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetailLocked;
import cn.need.cloud.biz.model.entity.inventory.InventoryLocked;
import cn.need.cloud.biz.model.entity.otb.*;
import cn.need.cloud.biz.model.param.base.WorkorderFinishUpdateParam;
import cn.need.cloud.biz.model.param.base.WorkorderStartUpdateParam;
import cn.need.cloud.biz.model.param.otb.update.request.OtbRequestCancelParam;
import cn.need.cloud.biz.model.query.base.SplitWorkorderDetailParam;
import cn.need.cloud.biz.model.query.base.SplitWorkorderParam;
import cn.need.cloud.biz.model.query.base.WorkorderRollbackListQuery;
import cn.need.cloud.biz.model.vo.base.BaseProductVersionVO;
import cn.need.cloud.biz.model.vo.base.auditlog.BaseProductLogVO;
import cn.need.cloud.biz.model.vo.base.pickingslip.BasePickingSlipUnpickDetailVO;
import cn.need.cloud.biz.model.vo.base.pickingslip.PickingSlipUnpickDetailVO;
import cn.need.cloud.biz.model.vo.base.workorder.PrepWorkorderFinishConfirmVO;
import cn.need.cloud.biz.model.vo.base.workorder.WorkorderConfirmDetailVO;
import cn.need.cloud.biz.model.vo.base.workorder.WorkorderConfirmVO;
import cn.need.cloud.biz.model.vo.base.workorder.WorkorderFinishConfirmVO;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailLockedService;
import cn.need.cloud.biz.service.helper.LockedHelper;
import cn.need.cloud.biz.service.helper.workorder.OtbWorkorderHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.OtbWorkorderAuditLogHelper;
import cn.need.cloud.biz.service.helper.workorder.WorkorderHelper;
import cn.need.cloud.biz.service.inventory.InventoryLockedService;
import cn.need.cloud.biz.service.otb.pallet.OtbPalletSpecialService;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPickingSlipSpecialService;
import cn.need.cloud.biz.service.otb.pkg.OtbPackageSpecialService;
import cn.need.cloud.biz.service.otb.putawayslip.OtbPutawaySlipDetailService;
import cn.need.cloud.biz.service.otb.putawayslip.OtbPutawaySlipService;
import cn.need.cloud.biz.service.otb.request.OtbRequestSpecialService;
import cn.need.cloud.biz.service.otb.shipment.OtbShipmentDetailService;
import cn.need.cloud.biz.service.otb.shipment.OtbShipmentSpecialService;
import cn.need.cloud.biz.service.otb.workorder.*;
import cn.need.cloud.biz.util.*;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.model.IdModel;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * OTB工单 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
@AllArgsConstructor(onConstructor_ = @Lazy)
public class OtbWorkorderSpecialServiceImpl implements OtbWorkorderSpecialService {

    private final OtbWorkorderService otbWorkorderService;
    private final OtbWorkorderDetailService otbWorkorderDetailService;
    private final OtbPrepWorkorderSpecialService otbPrepWorkorderSpecialService;
    private final OtbWorkorderBinLocationService otbWorkorderBinLocationService;
    private final OtbPickingSlipSpecialService otbPickingSlipSpecialService;
    private final OtbPutawaySlipService otbPutawaySlipService;
    private final OtbPutawaySlipDetailService otbPutawaySlipDetailService;
    private final OtbRequestSpecialService otbRequestSpecialService;
    private final OtbPackageSpecialService otbPackageSpecialService;
    private final OtbShipmentSpecialService otbShipmentSpecialService;
    private final OtbPalletSpecialService otbPalletSpecialService;
    private final InventoryLockedService inventoryLockedService;
    private final OtbShipmentDetailService otbShipmentDetailService;
    private final BinLocationDetailLockedService binLocationDetailLockedService;

    @NotNull
    private static List<OtbWorkorder> checkStartWorkorder(WorkorderProcessBO process, List<OtbWorkorder> workorderList) {
        Validate.notEmpty(workorderList, "idList: {} WorkOrder is empty", process.getIdList());

        String type = process.getProcessType().getType();
        workorderList.forEach(obj -> {
            Validate.isTrue(OtbWorkorderEnum.canStartStatuses().contains(obj.getOtbWorkorderStatus()),
                    ErrorConstant.STATUS_ERROR_FORMAT,
                    obj.refNumLog(), "start" + type, OtbWorkorderEnum.canStartStatuses(), obj.getOtbWorkorderStatus()
            );

            ProcessType.checkNormalAvailability(obj.getProcessType(), obj.refNumLog(), "start" + type);
        });
        return workorderList;
    }


    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean startRollback(WorkorderStartUpdateParam query) {
        WorkorderProcessBO process = BeanUtil.copyNew(query, WorkorderProcessBO.class);
        process.setProcessType(ProcessType.ROLLBACKING);
        this.processTriggering(process, this.getAndCheckStartWorkorder(process));
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean finishRollback(WorkorderFinishUpdateParam query) {
        WorkorderProcessBO process = BeanUtil.copyNew(query, WorkorderProcessBO.class);
        process.setProcessType(ProcessType.NORMAL);
        this.processTriggering(process, this.getAndCheckFinishWorkorder(process));
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startCancel(WorkorderStartUpdateParam query) {
        WorkorderProcessBO process = BeanUtil.copyNew(query, WorkorderProcessBO.class);
        process.setProcessType(ProcessType.CANCELLING);
        this.processTriggering(process, this.getAndCheckStartCancelWorkorder(process));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean finishCancel(WorkorderFinishUpdateParam query) {
        WorkorderProcessBO process = BeanUtil.copyNew(query, WorkorderProcessBO.class);
        process.setProcessType(ProcessType.NORMAL);

        // 获取工单
        List<OtbWorkorder> workorderList = this.getAndCheckFinishCancelWorkorder(process);
        Map<Long, OtbWorkorder> workorderMap = StreamUtils.toMap(workorderList, IdModel::getId);

        // 校验未Unpick 的工单
        List<PickingSlipUnpickDetailVO> unpickList = this.unpickList(query.getIdList());
        unpickList.forEach(unpick -> Validate.isTrue(unpick.getCanRollbackQty() == 0,
                "{} There are still remaining [{}, CanRollbackQty: {}]  unpicked items",
                workorderMap.get(unpick.getWorkorderId()).refNumLog(),
                Optional.ofNullable(unpick.getProductVersionId())
                        .map(ProductVersionCacheUtil::getById)
                        .map(pro -> BeanUtil.copyNew(pro, BaseProductVersionVO.class))
                        .map(BaseProductVersionVO::toString)
                        .orElse(StringPool.EMPTY),
                unpick.getCanRollbackQty()
        ));

        // 设置工单状态
        Map<Long, List<OtbWorkorderDetail>> detailsMap = otbWorkorderDetailService.groupByOtbWorkOrderIdList(query.getIdList());
        detailsMap.forEach((key, value) -> {
            OtbWorkorder workorder = workorderMap.get(key);
            boolean allCancel = value.stream().allMatch(detail -> detail.getPickedQty() == 0);
            workorder.setOtbWorkorderStatus(allCancel
                    ? OtbWorkorderEnum.CANCELLED.getStatus()
                    : OtbWorkorderEnum.PART_CANCELLED.getStatus()
            );
        });

        // 触发流程
        this.processTriggering(process, workorderList);

        // 包裹 FinishCancel
        otbPackageSpecialService.finishCancel(StreamUtils.distinctMap(workorderList, IdModel::getId));

        // 请求Cancel
        List<Long> requestIdList = StreamUtils.distinctMap(workorderList, OtbWorkorder::getOtbRequestId);
        otbRequestSpecialService.finishCancel(requestIdList);

        return true;
    }

    @Override
    public void rollbackPackedQty(OtbPackageRollbackSingleWorkorderBO rollback) {
        List<OtbPackage> readyToShipPkgList = rollback.getPkgList().stream()
                .toList();

        Map<Long, OtbPackage> readyToShipMap = StreamUtils.toMap(readyToShipPkgList, OtbPackage::getId);
        Map<Long, Integer> packedQtyWithProductMap = rollback.getPkgDetailList().stream()
                .filter(obj -> readyToShipMap.containsKey(obj.getOtbPackageId()))
                .collect(Collectors.groupingBy(OtbPackageDetail::getProductId, Collectors.summingInt(OtbPackageDetail::getQty)));

        OtbWorkorder workorder = rollback.getWorkorder();

        // 校验是否启用异常流程
        ProcessType.checkAbnormal(workorder.getProcessType(), workorder.refNumLog(), "rollbackReadyToShipQty");

        List<OtbWorkorderDetail> wkDetails = rollback.getWorkorderDetailList();
        List<ChangeQtyLogBO> readyToShipChangeList = wkDetails.stream()
                .filter(obj -> packedQtyWithProductMap.containsKey(obj.getProductId()))
                .map(detail -> {
                    Integer rollbackQty = packedQtyWithProductMap.get(detail.getProductId());
                    Validate.isTrue(detail.getPackedQty() >= rollbackQty,
                            String.format(ErrorMessages.INSUFFICIENT_QUANTITY, workorder.refNumLog(), "PackedQty", detail.getPackedQty(), rollbackQty)
                    );
                    int packedQty = detail.getPackedQty() - rollbackQty;
                    ChangeQtyLogBO change = new ChangeQtyLogBO();
                    change.setBeforeQty(detail.getPackedQty());
                    change.setAfterQty(packedQty);
                    change.setProductId(detail.getProductId());

                    detail.setPackedQty(packedQty);
                    return change;
                })
                .toList();

        // 工单 Rollback ReadyToShipQty 记录日志
        OtbWorkorderAuditLogHelper.recordLog(rollback.getWorkorder(), RollbackConstant.ROLLBACK_READY_PACKED_QTY,
                JsonUtil.toJson(readyToShipChangeList), rollback.getNote(), BaseTypeLogEnum.OPERATION.getType()
        );

        // 设置状态
        boolean picked = wkDetails.stream().allMatch(obj -> Objects.equals(obj.getPickedQty(), obj.getQty()));
        String oldStatus = rollback.getWorkorder().getOtbWorkorderStatus();
        rollback.getWorkorder().setOtbWorkorderStatus(picked
                ? OtbWorkorderEnum.PICKED.getStatus()
                : OtbWorkorderEnum.IN_PICKING.getStatus()
        );
        // 状态变更记录日志
        if (!Objects.equals(oldStatus, rollback.getWorkorder().getOtbWorkorderStatus())) {
            OtbWorkorderAuditLogHelper.recordLog(rollback.getWorkorder());
        }
    }

    @Override
    public void rollbackPacked(List<OtbPackageRollbackSingleWorkorderBO> rollbackList) {

        // 回滚
        rollbackList.forEach(this::rollbackPackedQty);

        // 工单
        List<OtbWorkorderDetail> workorderDetailList = rollbackList.stream()
                .flatMap(rollback -> rollback.getWorkorderDetailList().stream())
                .toList();
        Validate.isTrue(otbWorkorderDetailService.updateBatch(workorderDetailList) == workorderDetailList.size(),
                String.format(ErrorMessages.OPERATION_FAILED, "update WorkorderDetail rollback")
        );
        List<OtbWorkorder> workorderList = StreamUtils.distinctMap(rollbackList, OtbPackageRollbackSingleWorkorderBO::getWorkorder);
        Validate.isTrue(otbWorkorderService.updateBatch(workorderList) == workorderList.size(),
                String.format(ErrorMessages.OPERATION_FAILED, "update Workorder rollback")
        );
    }

    @Override
    public void cancelWithPickingSlip(List<Long> pickingSlipIdList) {
        // 获取拣货单下的工单
        List<OtbWorkorder> workorderList = otbWorkorderService.listByPickingSlipIds(pickingSlipIdList);

        // 回滚到Begin
        workorderList.forEach(workorder -> workorder.setOtbWorkorderStatus(OtbWorkorderEnum.BEGIN.getStatus()));

        // 更新工单
        Validate.isTrue(otbWorkorderService.updateBatch(workorderList) == workorderList.size(),
                String.format(ErrorMessages.OPERATION_FAILED, "update WorkOrder status")
        );
        OtbWorkorderAuditLogHelper.recordLog(workorderList);

        // 回滚 拣货单锁 -> 工单锁
        List<Long> workorderIds = StreamUtils.distinctMap(workorderList, IdModel::getId);
        List<OtbWorkorderDetail> details = otbWorkorderDetailService.listByWorkorderIds(workorderIds);
        List<Long> inventoryLockedIds = StreamUtils.distinctMap(details, OtbWorkorderDetail::getInventoryLockedId);

        List<InventoryLocked> inventoryLockedList = inventoryLockedService.listByIds(inventoryLockedIds);
        inventoryLockedList.forEach(locked -> {
            locked.setLockedStatus(InventoryLockedStatusEnum.LOCKED.getStatus());
            locked.setFinishQty(0);
        });

        Validate.isTrue(inventoryLockedService.updateBatch(inventoryLockedList) == inventoryLockedList.size(),
                String.format(ErrorMessages.OPERATION_FAILED, "update InventoryLocked")
        );

    }

    @Override
    public List<WorkorderConfirmDetailVO> confirmDetailList(WorkorderRollbackListQuery query) {
        Map<Long, OtbWorkorder> wkMap = StreamUtils.toMap(otbWorkorderService.listByIds(query.getIdList()), IdModel::getId);
        Map<Long, List<OtbWorkorderDetail>> detailsMap = otbWorkorderDetailService.groupByOtbWorkOrderIdList(query.getIdList());

        return detailsMap.entrySet()
                .stream()
                .flatMap(entry -> {
                    OtbWorkorder workorder = wkMap.get(entry.getKey());
                    return entry.getValue()
                            .stream()
                            .map(detail -> {
                                WorkorderConfirmDetailVO rollback = BeanUtil.copyNew(detail, WorkorderConfirmDetailVO.class);
                                WorkorderConfirmVO confirm = BeanUtil.copyNew(workorder, WorkorderConfirmVO.class);
                                confirm.setWorkorderStatus(workorder.getOtbWorkorderStatus());
                                rollback.setWorkorder(confirm);
                                return rollback;
                            });
                })
                .toList();
    }

    @Override
    public List<PickingSlipUnpickDetailVO> unpickList(List<Long> workorderIds) {
        // 处理已经创建上架单的不让他继续创建
        List<OtbPutawaySlipDetail> details = otbPutawaySlipDetailService.listAvailableByWorkorderIds(workorderIds);
        Map<Long, Integer> hasCreateQtyMap = details.stream()
                .collect(Collectors.groupingBy(OtbPutawaySlipDetail::getWorkorderBinLocationId, Collectors.summingInt(OtbPutawaySlipDetail::getQty)));

        List<OtbWorkorderBinLocation> currenWkPickInfoList = otbWorkorderBinLocationService.listByWorkorderIdList(workorderIds);
        // 扣除上架单占用数量
        currenWkPickInfoList.forEach(obj -> obj.setQty(obj.getQty() - hasCreateQtyMap.getOrDefault(obj.getId(), 0)));

        // 赋值返回参数
        List<PickingSlipUnpickDetailVO> unpickDetails = currenWkPickInfoList.stream()
                .map(obj -> {
                    PickingSlipUnpickDetailVO unpick = BeanUtil.copyNew(obj, PickingSlipUnpickDetailVO.class);
                    unpick.setWorkorderId(obj.getOtbWorkorderId());
                    unpick.setWorkorderDetailId(obj.getOtbWorkorderDetailId());
                    unpick.setWorkorderBinLocationId(obj.getId());
                    unpick.setPickingSlipId(obj.getOtbPickingSlipId());
                    unpick.setPickingSlipDetailId(obj.getOtbPickingSlipDetailId());
                    return unpick;
                })
                .toList();

        // 拣货数量即 是Unpick时 Rollback时的 qty
        unpickDetails.stream()
                .sorted(Comparator.comparing(BasePickingSlipUnpickDetailVO::getUnpickId))
                .forEach(detail -> detail.setPickedQty(detail.getQty()));

        // 获取工单详情
        List<Long> wkDetailIds = StreamUtils.distinctMap(currenWkPickInfoList, OtbWorkorderBinLocation::getOtbWorkorderDetailId);
        List<OtbWorkorderDetail> wkDetails = otbWorkorderDetailService.listByIds(wkDetailIds);
        Map<Long, OtbWorkorderDetail> wkDetailMap = StreamUtils.toMap(wkDetails, OtbWorkorderDetail::getId);

        Map<Long, Integer> readyToShipQtyMap = wkDetailMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, obj -> obj.getValue().getShippedQty()));

        // 赋值readyToShipQty、packedQty
        for (PickingSlipUnpickDetailVO unpickDetail : unpickDetails) {
            Integer remaining = readyToShipQtyMap.get(unpickDetail.getWorkorderDetailId());
            if (remaining <= 0) {
                continue;
            }
            int readyToShipQty = Math.min(remaining, unpickDetail.getPickedQty());
            unpickDetail.setReadyToShipQty(readyToShipQty);
            readyToShipQtyMap.put(unpickDetail.getWorkorderDetailId(), remaining - readyToShipQty);
        }

        return unpickDetails;
    }

    @Override
    public void rollback(OtbPutawaySlipPutAwayBO putawayParam) {
        OtbPutawaySlip putawaySlip = putawayParam.getPutawaySlip();

        List<OtbWorkorderDetail> wkDetails = otbWorkorderDetailService.listByWorkorderId(putawaySlip.getWorkorderId());
        OtbWorkorder workorder = otbWorkorderService.getById(putawaySlip.getWorkorderId());

        Map<Long, OtbWorkorderDetail> detailMap = StreamUtils.toMap(wkDetails, IdModel::getId);

        // Rollback
        List<ChangeQtyLogBO> changeList = putawayParam.getDetailList()
                .stream()
                .map(paramDetail -> {
                    OtbPutawaySlipDetail putawaySlipDetail = paramDetail.getPutawaySlipDetail();
                    OtbWorkorderDetail wkDetail = detailMap.get(putawaySlipDetail.getWorkorderDetailId());
                    wkDetail.setPickedQty(wkDetail.getPickedQty() - paramDetail.getPutawayQty());

                    // 变更数量
                    ChangeQtyLogBO change = new ChangeQtyLogBO();
                    change.setBeforeQty(wkDetail.getPickedQty() + paramDetail.getPutawayQty());
                    change.setAfterQty(wkDetail.getPickedQty());
                    change.setProductId(wkDetail.getProductId());

                    paramDetail.setWorkorderDetail(wkDetail);
                    return change;
                })
                .toList();

        // 状态变更 Picked/ReadyToShip -> IN_PICKING
        String oldStatus = workorder.getOtbWorkorderStatus();
        workorder.setOtbWorkorderStatus(OtbWorkorderEnum.IN_PICKING.getStatus());


        // rollback工单详情
        List<OtbWorkorderDetail> rollbackDetails = putawayParam.getDetailList().stream()
                .map(OtbPutawaySlipPutAwayDetailBO::getWorkorderDetail)
                .toList();

        Validate.isTrue(otbWorkorderDetailService.updateBatch(rollbackDetails) == rollbackDetails.size(),
                String.format(ErrorMessages.OPERATION_FAILED, "update WorkOrderDetail pickedQty")
        );
        // 记录日志
        OtbWorkorderAuditLogHelper.recordLog(workorder, "Rollback PickedQty", JsonUtil.toJson(changeList),
                putawayParam.getNote(), BaseTypeLogEnum.OPERATION.getType()
        );

        if (!Objects.equals(oldStatus, workorder.getOtbWorkorderStatus())) {
            Validate.isTrue(otbWorkorderService.update(workorder) == 1, String.format(ErrorMessages.OPERATION_FAILED, "update WorkOrder status"));
            OtbWorkorderAuditLogHelper.recordLog(workorder, null, putawayParam.getNote());
        }

    }

    @Override
    public WorkorderFinishConfirmVO finishConfirm(WorkorderRollbackListQuery query) {
        // Prep完成确认数据
        PrepWorkorderFinishConfirmVO prepFinishConfirm = otbPrepWorkorderSpecialService.finishRollbackConfirm(query);

        WorkorderFinishConfirmVO finishConfirm = new WorkorderFinishConfirmVO();
        finishConfirm.setWorkorderList(this.confirmDetailList(query));
        finishConfirm.setPutawaySlipList(otbPutawaySlipService.confirmDetailList(query));
        finishConfirm.setPrepWorkorderList(prepFinishConfirm.getPrepWorkorderList());
        finishConfirm.setPrepPutawaySlipList(prepFinishConfirm.getPrepPutawaySlipList());
        return finishConfirm;
    }

    @Override
    public void rollbackByShipment(List<OtbShipment> cancelList) {
        Validate.notEmpty(cancelList, "CancelList cannot be empty");

        // ShipmentDetail
        var shipmentIds = StreamUtils.distinctMap(cancelList, IdModel::getId);
        Validate.notEmpty(shipmentIds, "Cancel IdList {} cannot be empty", cancelList.size());

        var shipmentDetails = otbShipmentDetailService.listByOtbShipmentIds(shipmentIds);
        Validate.notEmpty(shipmentDetails, "Shipment IdList {} cannot be empty", shipmentIds);

        var shipmentMap = StreamUtils.toMap(cancelList, IdModel::getId);
        // 详情分组
        var shipmentDetailsGroupMap = shipmentDetails.stream()
                .collect(Collectors.groupingBy(obj -> StringUtil.format("{}:{}:{}:{}",
                        shipmentMap.get(obj.getOtbShipmentId()).getOtbWorkorderId(),
                        obj.getProductId(),
                        obj.getProductBarcode(),
                        obj.getProductChannelSku())
                ));

        // 工单
        var workorderIds = StreamUtils.distinctMap(cancelList, OtbShipment::getOtbWorkorderId);
        var workorderList = otbWorkorderService.listByIds(workorderIds);
        var workorderDetails = otbWorkorderDetailService.listByWorkorderIds(workorderIds);
        var workorderMap = StreamUtils.toMap(workorderList, IdModel::getId);
        // 回滚ShipmentQty
        for (var wkDetail : workorderDetails) {
            var unionKey = StringUtil.format("{}:{}:{}:{}",
                    wkDetail.getOtbWorkorderId(),
                    wkDetail.getDetailSnapshotProductBarcode(),
                    wkDetail.getDetailSnapshotProductChannelSku()
            );
            // 当前回滚的Shipment Detail
            shipmentDetailsGroupMap.getOrDefault(unionKey, Collections.emptyList())
                    .forEach(shipmentDetail -> {
                        var currentShipmentQty = wkDetail.getShipmentQty();
                        wkDetail.setShipmentQty(currentShipmentQty - shipmentDetail.getQty());

                        var change = new ChangeQtyLogBO();
                        change.setAfterQty(wkDetail.getShipmentQty());
                        change.setBeforeQty(currentShipmentQty);
                        change.setProductId(wkDetail.getProductId());

                        // 记录日志
                        OtbWorkorderAuditLogHelper.recordLog(workorderMap.get(wkDetail.getOtbWorkorderId()),
                                RollbackConstant.ROLLBACK_READY_SHIPMENT_QTY, JsonUtil.toJson(change)
                        );

                        Validate.isTrue(wkDetail.getShipmentQty() > 0,
                                "{} {} , BarCode: {} ChannelSku: {} ShipmentQty is not enough to rollback",
                                workorderMap.get(wkDetail.getOtbWorkorderId()).refNumLog(),
                                Optional.ofNullable(wkDetail.getProductId())
                                        .map(ProductCacheUtil::getById)
                                        .map(obj -> BeanUtil.copyNew(obj, BaseProductLogVO.class))
                                        .map(BaseProductLogVO::toLog)
                                        .orElse(StringPool.EMPTY),
                                wkDetail.getDetailSnapshotProductBarcode(),
                                wkDetail.getDetailSnapshotProductChannelSku()
                        );
                    });
        }

        // 更新工单状态
        workorderList.forEach(obj -> {
            obj.setOtbWorkorderStatus(OtbWorkorderEnum.PACKED.getStatus());

            OtbWorkorderAuditLogHelper.recordLog(obj);
        });

        Validate.isTrue(otbWorkorderService.updateBatch(workorderList) == workorderList.size(), "Update WorkOrder status failed");

        // 更新Request
        var requestIds = StreamUtils.distinctMap(workorderList, OtbWorkorder::getOtbRequestId);
        otbRequestSpecialService.rollbackByShipment(requestIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean split(List<SplitWorkorderParam> splitQueryList) {
        splitQueryList = WorkorderHelper.mergeSplitQuery(splitQueryList);

        Validate.notEmpty(splitQueryList, "SplitQueryList is must not empty");

        // 拆单逻辑
        var workorderIds = StreamUtils.distinctMap(splitQueryList, SplitWorkorderParam::getId);
        var workorderList = otbWorkorderService.listByIds(workorderIds);
        Validate.notEmpty(workorderList, "WorkOrderId: {} is not exist", workorderIds);

        workorderList.forEach(workorder -> {
            // 校验流程是否正确
            ProcessType.checkAbnormal(workorder.getProcessType(), workorder.refNumLog(), "split");
        });

        var details = otbWorkorderDetailService.listByWorkorderIds(workorderIds);
        var detailMap = StreamUtils.toMap(details, IdModel::getId);
        var workorderMap = StreamUtils.toMap(workorderList, IdModel::getId);

        // 需要拆单的详情
        checkSplitQuery(splitQueryList, detailMap, workorderMap);

        // Step: 工单 拆单
        var splitHolders = split(splitQueryList, workorderMap, detailMap);

        // Workorder & Split Workorder: Update Status
        OtbWorkorderHelper.refreshStatus(workorderList, details);
        OtbWorkorderHelper.refreshStatus(splitHolders.stream()
                        .map(OtbWorkorderSplitBO::getSplitWorkorder)
                        .toList(),
                splitHolders.stream()
                        .flatMap(obj -> obj.getDetailHolders().stream()
                                .map(OtbWorkorderSplitDetailBO::getSplitDetail))
                        .toList()
        );

        // Step: 拣货单：拆单
        otbPickingSlipSpecialService.split(splitHolders);

        // Step: Prep工单：拆单
        otbPrepWorkorderSpecialService.split(splitHolders);

        // Step: 锁拆单
        this.lockSplit(splitHolders);

        // 更新工单状态
        Validate.isTrue(otbWorkorderService.updateBatch(workorderList) == workorderList.size(),
                "Update WorkOrder status failed"
        );

        Validate.isTrue(otbWorkorderDetailService.updateBatch(details) == details.size(),
                "Update WorkOrderDetail status failed"
        );

        // 拆单入库
        var splitWorkorderList = StreamUtils.distinctMap(splitHolders, OtbWorkorderSplitBO::getSplitWorkorder);
        otbWorkorderService.insertBatch(splitWorkorderList);

        var splitDetails = splitHolders.stream()
                .flatMap(obj -> obj.getDetailHolders().stream().map(OtbWorkorderSplitDetailBO::getSplitDetail))
                .toList();

        otbWorkorderDetailService.insertBatch(splitDetails);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void allCancel(OtbRequestCancelParam param) {
        // 开起Cancel
        WorkorderStartUpdateParam query = new WorkorderStartUpdateParam();
        query.setRequestIdList(Collections.singletonList(param.getId()));
        this.startCancel(query);

        var workorderList = otbWorkorderService.listByRequestIds(query.getRequestIdList());
        var workorderDetails = otbWorkorderDetailService.listByWorkorderIds(StreamUtils.distinctMap(workorderList, IdModel::getId));

        // 全部取消
        var splitWkQueryList = workorderDetails.stream()
                .collect(Collectors.groupingBy(OtbWorkorderDetail::getOtbWorkorderId))
                .entrySet()
                .stream()
                .map(entry -> new SplitWorkorderParam(entry.getKey(), entry.getValue().stream()
                        // 只拆有拣货的并且还有未拣货的
                        .filter(obj -> obj.getPickedQty() > 0 && obj.getQty() > obj.getPickedQty())
                        .map(obj -> {
                            var splitWorkorderDetailQuery = new SplitWorkorderDetailParam();
                            splitWorkorderDetailQuery.setId(obj.getId());
                            splitWorkorderDetailQuery.setSplitQty(obj.getQty() - obj.getPickedQty());
                            return splitWorkorderDetailQuery;
                        })
                        .toList()))
                .filter(obj -> ObjectUtil.isNotEmpty(obj.getDetailList()))
                .toList();

        if (ObjectUtil.isEmpty(splitWkQueryList)) {
            return;
        }

        // 拆单
        this.split(splitWkQueryList);
    }

    @NotNull
    private static List<OtbWorkorderSplitBO> split(List<SplitWorkorderParam> splitQueryList, Map<Long, OtbWorkorder> workorderMap, Map<Long, OtbWorkorderDetail> detailMap) {
        return splitQueryList.stream()
                .map(obj -> {
                    // 拆单
                    var workorder = workorderMap.get(obj.getId());
                    var split = BeanUtil.copyNew(workorder, OtbWorkorder.class);
                    split.setId(IdWorker.getId());
                    split.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.OTC_WORK_ORDER.getCode()));
                    split.setOtbPickingSlipId(null);

                    var splitDetails = obj.getDetailList()
                            .stream()
                            .map(o -> {
                                var detail = detailMap.get(o.getId());
                                // 减去拆单数量
                                detail.setQty(detail.getQty() - o.getSplitQty());

                                // 拆单详情
                                var splitDetail = BeanUtil.copyNew(detail, OtbWorkorderDetail.class);
                                splitDetail.setId(IdWorker.getId());
                                splitDetail.setOtbWorkorderId(split.getId());

                                // 初始化数量
                                splitDetail.setQty(o.getSplitQty());
                                splitDetail.setFinishQty(0);
                                splitDetail.setPackedQty(0);
                                splitDetail.setPickedQty(0);
                                splitDetail.setShipmentQty(0);
                                splitDetail.setShippedQty(0);
                                splitDetail.setFinishReserveQty(0);
                                splitDetail.setReserveQty(0);

                                var splitDetailHolder = new OtbWorkorderSplitDetailBO();
                                splitDetailHolder.setSplitDetail(splitDetail);
                                splitDetailHolder.setDetail(detail);
                                splitDetailHolder.setSplitQty(o.getSplitQty());
                                return splitDetailHolder;
                            })
                            .toList();

                    var splitHolder = new OtbWorkorderSplitBO();
                    splitHolder.setWorkorder(workorder);
                    splitHolder.setSplitWorkorder(split);
                    splitHolder.setDetailHolders(splitDetails);
                    return splitHolder;
                })
                .toList();
    }

    /**
     * 检查拆单条件
     *
     * @param splitQueryList 拆单条件
     * @param detailMap      工单详情
     * @param workorderMap   工单
     */
    private static void checkSplitQuery(List<SplitWorkorderParam> splitQueryList, Map<Long, OtbWorkorderDetail> detailMap, Map<Long, OtbWorkorder> workorderMap) {
        splitQueryList.stream()
                .map(SplitWorkorderParam::getDetailList)
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(SplitWorkorderDetailParam::getId, Collectors.summingInt(SplitWorkorderDetailParam::getSplitQty)))
                .forEach((key, splitQty) -> {
                    // 校验拆单数量是否合法
                    var detail = detailMap.get(key);
                    Validate.notNull(detail, "Detail id {} is not exist", key);

                    var productLog = Optional.ofNullable(detail.getProductId())
                            .map(ProductCacheUtil::getById)
                            .map(obj -> BeanUtil.copyNew(obj, BaseProductLogVO.class))
                            .map(BaseProductLogVO::toLog)
                            .orElse(StringPool.EMPTY);

                    var wkLog = workorderMap.get(detail.getOtbWorkorderId()).refNumLog();
                    // 有拣货且有未拣货的才能拆单
                    Validate.isTrue(detail.getPickedQty() > 0 && detail.getQty() > detail.getPickedQty(),
                            "{} Detail {} cannot be split",
                            wkLog, productLog
                    );

                    var canSplitQty = detail.getQty() - detail.getPickedQty();
                    Validate.isTrue(splitQty <= canSplitQty,
                            "{} Detail {} not have enough units to split the WorkOrder, [AvailableSplitQty: {} < SplitQty: {}]",
                            wkLog, productLog, canSplitQty, splitQty
                    );
                });
    }

    /**
     * 锁 拆单
     *
     * @param splitHolders 拆单信息
     */
    private void lockSplit(List<OtbWorkorderSplitBO> splitHolders) {
        // 工单锁
        this.inventoryLockedSplit(splitHolders);

        // 上架锁
        this.putawayLockedSplit(splitHolders);
    }

    /**
     * 工单锁拆分
     *
     * @param splitHolders splitHolders
     */
    private void inventoryLockedSplit(List<OtbWorkorderSplitBO> splitHolders) {
        // 原单 释放锁
        var lockedIds = splitHolders.stream()
                .flatMap(obj -> obj.getDetailHolders().stream())
                .map(obj -> obj.getSplitDetail().getInventoryLockedId())
                .toList();
        var lockedList = inventoryLockedService.listByIds(lockedIds);
        var lockedMap = StreamUtils.toMap(lockedList, IdModel::getId);

        // InventoryLocked 拆单
        var splitLockedList = splitHolders.stream()
                .flatMap(holder -> holder.getDetailHolders().stream()
                        .map(detailHolder -> {
                            var currentLocked = lockedMap.get(detailHolder.getDetail().getInventoryLockedId());
                            currentLocked.setQty(currentLocked.getQty() - detailHolder.getSplitQty());
                            // FinishQty
                            var splitFinishQty = currentLocked.getFinishQty() > currentLocked.getQty()
                                    ? currentLocked.getFinishQty() - currentLocked.getQty() : 0;
                            currentLocked.setFinishQty(currentLocked.getFinishQty() - splitFinishQty);
                            LockedHelper.statusRefresh(currentLocked);

                            // InventoryLocked 拆单
                            var splitLocked = BeanUtil.copyNew(currentLocked, InventoryLocked.class);
                            splitLocked.setId(IdWorker.getId());

                            // 设置数量更新状态
                            splitLocked.setQty(detailHolder.getSplitQty());
                            splitLocked.setFinishQty(splitFinishQty);
                            LockedHelper.statusRefresh(splitLocked);

                            splitLocked.setRefTableShowRefNum(holder.getSplitWorkorder().getRefNum());
                            var splitDetail = detailHolder.getSplitDetail();
                            splitLocked.setRefTableName(String.valueOf(splitDetail.getLineNum()));
                            splitLocked.setRefTableId(splitDetail.getId());
                            return splitLocked;
                        }))
                .toList();
        inventoryLockedService.insertBatch(splitLockedList);

        Validate.isTrue(inventoryLockedService.updateBatch(lockedList) == lockedList.size(),
                "Update InventoryLocked qty fail"
        );
    }

    /**
     * 上架锁拆分
     *
     * @param splitHolders splitHolders
     */
    private void putawayLockedSplit(List<OtbWorkorderSplitBO> splitHolders) {
        var refTableIds = splitHolders.stream()
                .flatMap(obj -> obj.getDetailHolders().stream().map(OtbWorkorderSplitDetailBO::getDetail).map(IdModel::getId))
                .toList();

        var binLockedList = binLocationDetailLockedService.listByRefTableIds(refTableIds);
        var lockedsMap = StreamUtils.groupBy(binLockedList, BinLocationDetailLocked::getRefTableId);

        // 拆单上锁
        var splitWorkorderMap = splitHolders.stream()
                .map(OtbWorkorderSplitBO::getSplitWorkorder)
                .collect(Collectors.toMap(IdModel::getId, Function.identity()));
        var splitLockedList = splitHolders.stream()
                .flatMap(obj -> obj.getDetailHolders().stream())
                .flatMap(detailHolder -> {
                    var currentLockedList = lockedsMap.get(detailHolder.getDetail().getId());

                    var allocations = BeanUtil.copyNew(currentLockedList, AllocationBO.class);

                    // 分配拆单数量
                    AllocationUtil.checkAndAllocationQty(allocations, detailHolder.getSplitQty());

                    var currentLockedMap = StreamUtils.toMap(currentLockedList, IdModel::getId);
                    return allocations.stream()
                            .filter(allocation -> allocation.getChangeQty() > 0)
                            .map(allocation -> {
                                var changeQty = allocation.getChangeQty();

                                var currentLocked = currentLockedMap.get(allocation.getId());
                                currentLocked.setQty(currentLocked.getQty() - changeQty);
                                // FinishQty
                                var splitFinishQty = currentLocked.getFinishQty() > currentLocked.getQty()
                                        ? currentLocked.getFinishQty() - currentLocked.getQty() : 0;
                                currentLocked.setFinishQty(currentLocked.getFinishQty() - splitFinishQty);
                                LockedHelper.statusRefresh(currentLocked);

                                // 拆单上锁
                                BinLocationDetailLocked locked = BeanUtil.copyNew(currentLocked, BinLocationDetailLocked.class);
                                locked.setId(IdWorker.getId());

                                locked.setQty(changeQty);
                                // 工单状态判断
                                locked.setFinishQty(splitFinishQty);
                                LockedHelper.statusRefresh(locked);

                                var splitDetail = detailHolder.getSplitDetail();
                                locked.setRefTableId(splitDetail.getId());
                                locked.setRefTableRefNum(String.valueOf(splitDetail.getLineNum()));
                                Optional.ofNullable(splitWorkorderMap.get(splitDetail.getOtbWorkorderId()))
                                        .ifPresent(slip -> locked.setRefTableShowRefNum(slip.getRefNum()));

                                return locked;
                            });
                })
                .toList();

        binLocationDetailLockedService.insertBatch(splitLockedList);

        Validate.isTrue(binLocationDetailLockedService.updateBatch(binLockedList) == binLockedList.size(),
                "Update BinLocationLocked qty fail"
        );
    }

    /**
     * 流程触发
     *
     * @param process       触发工单条件
     * @param workorderList 触发的工单
     */
    private void processTriggering(WorkorderProcessBO process, List<OtbWorkorder> workorderList) {
        String type = process.getProcessType().getType();
        // 设置流程状态
        workorderList.forEach(obj -> {
            obj.setProcessType(type);
            if (ProcessType.abnormal().contains(type)) {
                obj.setNote(process.getNote());
            }
        });

        Validate.isTrue(otbWorkorderService.updateBatch(workorderList) == workorderList.size(),
                "Update WorkOrder status [{}] failed", type
        );

        // 记录日志
        OtbWorkorderAuditLogHelper.recordLog(workorderList, type, null, process.getNote(), BaseTypeLogEnum.PROCESS_TYPE.getType());

        process.setPickingSlipIds(StreamUtils.distinctMap(workorderList, OtbWorkorder::getOtbPickingSlipId));

        // 拣货单 流程触发
        otbPickingSlipSpecialService.processTriggering(process);

        // 包裹：流程触发
        otbPackageSpecialService.processTriggering(process);

        // Pallet: 流程触发
        otbPalletSpecialService.processTriggering(process);

        // Shipment: 流程触发
        otbShipmentSpecialService.processTriggering(process);

        // Prep 流程触发
        var workorderPrepIds = workorderList.stream()
                .filter(obj -> !Objects.equals(obj.getWorkorderPrepStatus(), WorkOrderPrepStatusEnum.NONE.getStatus()))
                .map(IdModel::getId)
                .toList();
        process.setHasPrepWorkorderIds(workorderPrepIds);
        otbPrepWorkorderSpecialService.processTriggering(process);

    }

    /**
     * 获取并校验start工单
     *
     * @param process 流程条件
     * @return /
     */
    private List<OtbWorkorder> getAndCheckStartWorkorder(WorkorderProcessBO process) {
        List<OtbWorkorder> workorderList = otbWorkorderService.listByIds(process.getIdList());

        return checkStartWorkorder(process, workorderList);
    }

    /**
     * 获取并校验start工单
     *
     * @param process 流程条件
     * @return /
     */
    private List<OtbWorkorder> getAndCheckStartCancelWorkorder(WorkorderProcessBO process) {
        List<OtbWorkorder> workorderList = otbWorkorderService.listByRequestIds(process.getRequestIdList());

        return checkStartWorkorder(process, workorderList);
    }

    /**
     * 获取并校验finish工单
     *
     * @param process 流程条件
     * @return /
     */
    private List<OtbWorkorder> getAndCheckFinishWorkorder(WorkorderProcessBO process) {
        List<OtbWorkorder> workorderList = otbWorkorderService.listByIds(process.getIdList());
        return this.checkFinishWorkorder(process, workorderList);
    }

    @NotNull
    private List<OtbWorkorder> checkFinishWorkorder(WorkorderProcessBO process, List<OtbWorkorder> workorderList) {
        Validate.notEmpty(workorderList, "WorkOrderId: {} is not exist", process.getIdList());

        // 校验上架是否全部完成
        otbPutawaySlipService.finishRollback(workorderList);

        workorderList.forEach(obj -> ProcessType.checkAbnormal(obj.getProcessType(), obj.refNumLog(), "finish"));
        return workorderList;
    }

    /**
     * 获取并校验finish工单
     *
     * @param process 流程条件
     * @return /
     */
    private List<OtbWorkorder> getAndCheckFinishCancelWorkorder(WorkorderProcessBO process) {
        List<OtbWorkorder> workorderList = otbWorkorderService.listByIds(process.getIdList());
        return this.checkFinishWorkorder(process, workorderList);
    }
}
