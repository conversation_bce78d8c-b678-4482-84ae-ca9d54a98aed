package cn.need.cloud.biz.model.vo.base;

import cn.need.cloud.biz.model.vo.base.aware.BaseTransactionPartnerShowVO;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 产品基本信息 vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@Schema(description = "产品版本信息 vo对象")
public class BaseProductVersionVO extends BaseTransactionPartnerShowVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    @Schema(description = "产品版本ID")
    private Long id;

    @Schema(description = "产品ID")
    private Long productId;


    // @Schema(description = "交易伙伴ID")
    // private Long transactionPartnerId;

    @Schema(description = "产品重新测量类型")
    private String productRemeasureType;

    @Schema(description = "产品版本号")
    private Integer productVersionInt;

    @Schema(description = "参考编号")
    private String refNum;

    @Schema(description = "UPC编号")
    private String upc;

    @Schema(description = "供应商SKU")
    private String supplierSku;

    @Schema(description = "产品标题")
    private String title;

    @Override
    public String toString() {
        if (ObjectUtil.isEmpty(this.getTransactionPartnerVO())) {
            return StringUtil.format(" Product:{}/{}/{} ", supplierSku, upc, productVersionInt);
        } else {
            return StringUtil.format(" Product:{}/{}/{}/{} ", this.getTransactionPartnerVO()
                    .getAbbrName(), supplierSku, upc, productVersionInt);
        }
    }

}
