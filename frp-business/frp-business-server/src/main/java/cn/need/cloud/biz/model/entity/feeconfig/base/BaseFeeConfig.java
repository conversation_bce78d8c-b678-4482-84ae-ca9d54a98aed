package cn.need.cloud.biz.model.entity.feeconfig.base;

import cn.need.cloud.biz.model.entity.base.FeeConfigModel;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public abstract class BaseFeeConfig extends FeeConfigModel {

    /**
     * 是否有效(0-无效，1-有效)
     */
    @TableField("active_flag")
    private Boolean activeFlag;

    /**
     * 计费条件类型（决定区间判断的依据）
     */
    @TableField("condition_type")
    private String conditionType;

    /**
     * 货币代码（如USD、CNY）
     */
    @TableField("currency")
    private String currency;

    /**
     * 删除原因
     */
    @TableField("deleted_note")
    private String deletedNote;

    /**
     * 额外计费判断字段
     */
    @TableField("extra_fee_judge_fields")
    private String extraFeeJudgeFields;

    /**
     * 费用计费类型
     */
    @TableField("fee_calculation_type")
    private String feeCalculationType;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 备注
     */
    @TableField("note")
    private String note;
}