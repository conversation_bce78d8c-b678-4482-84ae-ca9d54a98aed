package cn.need.cloud.biz.service.otb.workorder.impl;

import cn.need.cloud.biz.client.constant.enums.base.PrepWorkOrderDetailTypeEnum;
import cn.need.cloud.biz.client.constant.enums.base.PrepWorkOrderTypeEnum;
import cn.need.cloud.biz.client.constant.enums.inventory.WorkOrderPrepStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otb.OtbPrepWorkOrderEnum;
import cn.need.cloud.biz.client.constant.enums.otb.WorkorderProductTypeEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcPrepWorkorderStatusEnum;
import cn.need.cloud.biz.client.constant.enums.product.ProductConfigTypeEnum;
import cn.need.cloud.biz.client.constant.workorder.WorkorderLogConstant;
import cn.need.cloud.biz.converter.otb.OtbPrepWorkorderConverter;
import cn.need.cloud.biz.mapper.auto.OtbPrepWorkorderAutoMapper;
import cn.need.cloud.biz.mapper.otb.OtbPrepWorkorderMapper;
import cn.need.cloud.biz.model.bo.common.RefTableBO;
import cn.need.cloud.biz.model.entity.inventory.InventoryReserve;
import cn.need.cloud.biz.model.entity.otb.*;
import cn.need.cloud.biz.model.query.auto.OtbPrepWorkorderAutoQuery;
import cn.need.cloud.biz.model.query.base.BaseBinLocationQuery;
import cn.need.cloud.biz.model.query.otb.pickingslip.prep.OtbPrepPickingSlipFilterBuildQuery;
import cn.need.cloud.biz.model.query.otb.workorder.prep.OtbPrepWorkOrderListQuery;
import cn.need.cloud.biz.model.query.otb.workorder.prep.OtbPrepWorkorderQuery;
import cn.need.cloud.biz.model.vo.base.BasePickVO;
import cn.need.cloud.biz.model.vo.base.RefNumVO;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPrepPickingSlipDetailPickVO;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPrepPickingSlipPickContextVO;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPrepPickingSlipPutAwayContextVO;
import cn.need.cloud.biz.model.vo.otb.workorder.OtbPrepWorkorderDetailPickVO;
import cn.need.cloud.biz.model.vo.otb.workorder.OtbPrepWorkorderDetailVO;
import cn.need.cloud.biz.model.vo.otb.workorder.OtbPrepWorkorderVO;
import cn.need.cloud.biz.model.vo.otb.workorder.OtbPrepWorkorderWorkorderVO;
import cn.need.cloud.biz.model.vo.page.OtbPrepWorkorderPageVO;
import cn.need.cloud.biz.service.base.PickingSlipService;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.OtbPrepWorkorderAuditLogHelper;
import cn.need.cloud.biz.service.inventory.InventoryReserveService;
import cn.need.cloud.biz.service.otb.workorder.OtbPrepWorkorderBinLocationService;
import cn.need.cloud.biz.service.otb.workorder.OtbPrepWorkorderDetailService;
import cn.need.cloud.biz.service.otb.workorder.OtbPrepWorkorderService;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderService;
import cn.need.cloud.biz.service.product.ProductSpecialService;
import cn.need.cloud.biz.util.AllocationUtil;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * OTB预提工单 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtbPrepWorkorderServiceImpl extends SuperServiceImpl<OtbPrepWorkorderMapper, OtbPrepWorkorder> implements OtbPrepWorkorderService {

    @Resource
    @Lazy
    private OtbWorkorderService otbWorkorderService;
    @Resource
    @Lazy
    private OtbPrepWorkorderBinLocationService otbPrepWorkorderBinLocationService;
    @Resource
    @Lazy
    private OtbPrepWorkorderDetailService otbPrepWorkorderDetailService;
    @Resource
    private ProductSpecialService productSpecialService;
    @Resource
    private InventoryReserveService inventoryReserveService;
    @Resource
    private PickingSlipService pickingSlipService;

    @Resource
    private OtbPrepWorkorderAutoMapper prepWorkorderAutoMapper;

    /**
     * 修复库位查询条件为空对象，影响SQL语句拼接
     *
     * @param query 工单列表查询条件
     * @return /
     */
    private static boolean checkAndFixBinLocationQuery(OtbPrepWorkOrderListQuery query) {
        OtbPrepWorkorderQuery otcWorkorderQuery = query.getOtbPrepWorkorderQuery();
        BaseBinLocationQuery baseBinLocationQuery = query.getBinLocationQuery();
        boolean canBinLocationQuery = ObjectUtil.isNotNull(otcWorkorderQuery)
                // 仅Begin状态才开启
                && Objects.equals(otcWorkorderQuery.getOtbPrepWorkorderStatus(), OtbPrepWorkOrderEnum.BEGIN.getStatus())
                // 添加库位查询条件
                && ObjectUtil.isNotNull(baseBinLocationQuery)
                // 至少存在一个条件
                && baseBinLocationQuery.enable();
        if (!canBinLocationQuery) {
            // 未开启库位查询条件 库位查询条件设置null，空对象影响查询SQL
            query.setBinLocationQuery(null);
        }
        return canBinLocationQuery;
    }

    /**
     * FilterBuild条件校验
     *
     * @param workOrderListQuery FilterBuild工单条件
     */
    private static void checkFilterBuildWorkOrder(OtbPrepWorkOrderListQuery workOrderListQuery) {
        OtbPrepWorkorderQuery otbWorkorderQuery = workOrderListQuery.getOtbPrepWorkorderQuery();

        // 仅仅Begin状态Filter Build Picking Slip
        boolean notBeginWorkOrderStatus = !OtbPrepWorkOrderEnum.BEGIN.getStatus().equals(otbWorkorderQuery.getOtbPrepWorkorderStatus());
        if (notBeginWorkOrderStatus) {
            throw new BusinessException("Only support Begin Work Order status");
        }
    }

    /**
     * 构建Prep工单拣货信息
     *
     * @param pickingSlipPickList           Prep拣货单拣货信息
     * @param prepDetailGroupByProductIdMap Prep工单详情产品分组
     * @return /
     */
    @NotNull
    private static List<OtbPrepWorkorderDetailPickVO> buildPickList(List<OtbPrepPickingSlipDetailPickVO> pickingSlipPickList,
                                                                    Map<Long, List<OtbPrepWorkorderDetail>> prepDetailGroupByProductIdMap,
                                                                    Map<Long, OtbPrepWorkorder> prepWorkOrderMap) {
        // 非空校验
        Validate.notNull(pickingSlipPickList, "Picking slip pick list cannot be null");

        // 根据工单RefNum排序 分配产品
        prepDetailGroupByProductIdMap.forEach((key, detailList) ->
                detailList.sort(Comparator.comparing(o -> prepWorkOrderMap.get(o.getOtbPrepWorkorderId()).getRefNum())));

        // 分配
        List<OtbPrepWorkorderDetailPickVO> result = AllocationUtil.checkAndAllocationPickQty(
                pickingSlipPickList,
                prepDetailGroupByProductIdMap,
                (detail, pick) -> buildPrepWorkorderDetailPick(prepWorkOrderMap, pick, detail)
        );

        // 确保返回结果不为null
        if (result == null) {
            throw new BusinessException("AllocationUtil.checkAndAllocationPickQty returned null");
        }

        return result;
    }

    /**
     * 构建工单详情拣货信息
     *
     * @param prepWorkOrderMap Prep工单
     * @param pick             拣货单拣货信息
     * @param detail           Prep工单详情
     * @return /
     */
    @NotNull
    private static OtbPrepWorkorderDetailPickVO buildPrepWorkorderDetailPick(Map<Long, OtbPrepWorkorder> prepWorkOrderMap,
                                                                             OtbPrepPickingSlipDetailPickVO pick,
                                                                             OtbPrepWorkorderDetail detail) {
        Validate.notNull(prepWorkOrderMap, "Prep Work Order Map can not null");
        Validate.notNull(pick, "Pick can not null");
        Validate.notNull(detail, "Detail can not null");


        OtbPrepWorkorderDetailPickVO workorderPick = BeanUtil.copyNew(detail, OtbPrepWorkorderDetailPickVO.class);

        workorderPick.setProductId(pick.getProductId());
        workorderPick.setProductVersionId(pick.getProductVersionId());

        // 设置库位相关信息
        workorderPick.setBinLocationId(pick.getBinLocationId());
        workorderPick.setBinLocationDetailId(pick.getBinLocationDetailId());
        workorderPick.setBinLocationDetailLockedId(pick.getBinLocationDetailLockedId());

        // 设置拣货单信息
        workorderPick.setOtbPrepPickingSlipDetailId(pick.getId());
        workorderPick.setOtbPrepPickingSlipId(pick.getOtbPrepPickingSlipId());
        workorderPick.setOtbWorkorderId(prepWorkOrderMap.get(workorderPick.getOtbPrepWorkorderId()).getOtbWorkorderId());
        workorderPick.setOtbWorkorderDetailId(prepWorkOrderMap.get(workorderPick.getOtbPrepWorkorderId()).getOtbWorkorderDetailId());

        // 锁相关信息
        workorderPick.setRefTableId(workorderPick.getId());
        workorderPick.setRefTableRefNum(String.valueOf(workorderPick.getLineNum()));
        workorderPick.setRefTableName(OtbPrepWorkorderDetail.class.getSimpleName());
        workorderPick.setRefTableShowName(OtbPrepWorkorder.class.getSimpleName());
        workorderPick.setRefTableShowRefNum(Optional.ofNullable(prepWorkOrderMap.get(detail.getOtbPrepWorkorderId()))
                .map(OtbPrepWorkorder::getRefNum)
                .orElse(String.valueOf(workorderPick.getOtbPrepWorkorderId()))
        );

        // 库位日志信息
        RefTableBO logInfo = new RefTableBO();
        logInfo.setRefTableId(pick.getRefTableId());
        logInfo.setRefTableRefNum(pick.getRefTableRefNum());
        logInfo.setRefTableName(pick.getRefTableName());
        logInfo.setRefTableShowName(pick.getRefTableShowName());
        logInfo.setRefTableShowRefNum(pick.getRefTableShowRefNum());
        workorderPick.setPickLogInfo(logInfo);

        workorderPick.setPickedBeforeQty(detail.getPickedQty());
        return workorderPick;
    }

    @Override
    public PageData<OtbPrepWorkorderPageVO> pageByQuery(PageSearch<OtbPrepWorkOrderListQuery> search) {
        Page<OtbPrepWorkorder> page = Conditions.page(search, entityClass);
        OtbPrepWorkOrderListQuery condition = search.getCondition();
        // 存在库位条件的处理
        if (this.fixQueryByBinLocation(condition)) {
            return new PageData<>(Collections.emptyList(), page);
        }
        List<OtbPrepWorkorderPageVO> dataList = mapper.listByQuery(condition.getOtbPrepWorkorderQuery(), condition.getBinLocationQuery(), page);
        // 填充字段
        this.fillPageField(dataList);
        return new PageData<>(dataList, page);
    }

    @Override
    public PageData<OtbPrepWorkorderPageVO> pageV1ByQuery(PageSearch<OtbPrepWorkorderAutoQuery> search) {
        Page<OtbPrepWorkorder> page = Conditions.page(search, entityClass);
        List<OtbPrepWorkorderPageVO> dataList = prepWorkorderAutoMapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public OtbPrepWorkorderVO detailById(Long id) {
        OtbPrepWorkorder entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("id: " + id + " not found in OtbPrepWorkorder");
        }
        return buildOtbPrepWorkorderVO(entity);
    }

    @Override
    public OtbPrepWorkorderVO detailByRefNum(String refNum) {
        OtbPrepWorkorder entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("RefNum: " + refNum + " not found in OtbPrepWorkorder");
        }
        return buildOtbPrepWorkorderVO(entity);
    }

    @Override
    public List<OtbPrepWorkorder> listByOtbWorkOrderIdList(List<Long> workOrderIdList) {
        if (ObjectUtil.isEmpty(workOrderIdList)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(OtbPrepWorkorder::getOtbWorkorderId, workOrderIdList).list();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<OtbPrepWorkorder> workOrderBegin(Set<Long> workOrderIdList) {
        if (ObjectUtil.isEmpty(workOrderIdList)) {
            return Collections.emptyList();
        }
        List<OtbPrepWorkorder> prepWorkorderList = this.lambdaQuery()
                .in(OtbPrepWorkorder::getOtbWorkorderId, workOrderIdList)
                .list();
        this.doBegin(prepWorkorderList);
        return prepWorkorderList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void begin(Set<Long> prepWorkOrderIdList) {
        if (ObjectUtil.isEmpty(prepWorkOrderIdList)) {
            return;
        }
        // 执行begin
        this.doBegin(this.listByIds(prepWorkOrderIdList));
    }

    @Override
    public List<OtbPrepWorkorder> filterBuildByQuery(OtbPrepPickingSlipFilterBuildQuery query) {
        OtbPrepWorkOrderListQuery workOrderListQuery = query.getFilter();
        // 校验
        checkFilterBuildWorkOrder(workOrderListQuery);
        // 修复库位查询条件
        checkAndFixBinLocationQuery(workOrderListQuery);
        // 这里查询没有校验库存
        return mapper.filterBuild(workOrderListQuery.getOtbPrepWorkorderQuery(), workOrderListQuery.getBinLocationQuery());
    }

    @Override
    public long filterBuildPickingSlipCount(OtbPrepWorkOrderListQuery query) {
        // 校验
        checkFilterBuildWorkOrder(query);
        // 修复仓库条件空对象SQL问题
        if (this.fixQueryByBinLocation(query)) {
            return 0L;
        }
        // 返回列表条数
        return mapper.filterBuildPickingSlipCount(query.getOtbPrepWorkorderQuery(), query.getBinLocationQuery());
    }

    @Override
    public Map<Long, List<OtbPrepWorkorder>> groupByOtbPrepPickingSlipIdList(List<Long> prepPickingSlipIdList) {
        if (ObjectUtil.isEmpty(prepPickingSlipIdList)) {
            return Collections.emptyMap();
        }
        // 按拣货单分组
        return lambdaQuery()
                .in(OtbPrepWorkorder::getOtbPrepPickingSlipId, prepPickingSlipIdList)
                .list()
                .stream()
                .collect(Collectors.groupingBy(OtbPrepWorkorder::getOtbPrepPickingSlipId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pick(OtbPrepPickingSlipPickContextVO context) {
        OtbPrepPickingSlip prepPickingSlip = context.getPrepPickingSlip();
        // 工单
        Map<Long, OtbPrepWorkorder> prepWorkOrderMap = this.findCanPickListByPickingSlip(prepPickingSlip);
        List<Long> prepWorkOrderIdList = prepWorkOrderMap.keySet().stream().toList();

        // 工单详情分组
        List<OtbPrepWorkorderDetail> prepWorkOrderDetailList = otbPrepWorkorderDetailService.groupByOtbPrepWorkOrderIdList(prepWorkOrderIdList)
                .values().stream()
                .flatMap(Collection::stream)
                .toList();

        // 产品工单详情分组
        Map<Long, List<OtbPrepWorkorderDetail>> prepDetailGroupByProductIdMap = prepWorkOrderDetailList
                .stream()
                .collect(Collectors.groupingBy(OtbPrepWorkorderDetail::getProductId));

        // 构建Prep工单详情拣货信息
        List<OtbPrepWorkorderDetailPickVO> prepWorkOrderDetailPickUpdateList
                = buildPickList(context.getPickAfterPrepDetailList(), prepDetailGroupByProductIdMap, prepWorkOrderMap);

        // 更新库位锁并扣减库位库存
        pickingSlipService.otbMoveBinLocationInventoryToReadyToGo(prepWorkOrderDetailPickUpdateList);

        // 更新拣货数量
        this.updatePickedQtyAndPicked(prepWorkOrderMap, prepWorkOrderDetailList, prepWorkOrderDetailPickUpdateList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAndSetWorkOrderProcessed(OtbPrepPickingSlipPutAwayContextVO context) {
        List<OtbPrepWorkorder> prepWorkOrderList = context.getPutAwayPrepWorkOrderList();
        if (ObjectUtil.isEmpty(prepWorkOrderList)) {
            return;
        }

        // 已经处理完成Prep工单
        List<OtbPrepWorkorder> processedList = prepWorkOrderList.stream()
                .filter(obj -> Objects.equals(obj.getOtbPrepWorkorderStatus(), OtbPrepWorkOrderEnum.PROCESSED.getStatus()))
                .toList();

        List<Long> workOrderIdList = StreamUtils.distinctMap(processedList, OtbPrepWorkorder::getOtbWorkorderId);

        int prepWorkOrderCount = this.updateBatch(prepWorkOrderList);
        Validate.isTrue(prepWorkOrderCount == prepWorkOrderList.size(), "Failed to update prep work order");

        // 设置工单状态为已处理
        Map<Long, List<OtbPrepWorkorder>> prepWorkorderGroupByWkMap = this.groupByWorkorderIdList(workOrderIdList);

        List<Long> processedWorkorderIdList = prepWorkorderGroupByWkMap.entrySet()
                .stream()
                .filter(entry -> entry.getValue()
                        .stream()
                        .allMatch(prep -> prep.getOtbPrepWorkorderStatus().equals(OtbPrepWorkOrderEnum.PROCESSED.getStatus())))
                .map(Map.Entry::getKey)
                .toList();

        List<OtbWorkorder> workorderList = otbWorkorderService.listByIds(processedWorkorderIdList);
        workorderList.forEach(obj -> obj.setWorkorderPrepStatus(WorkOrderPrepStatusEnum.PROCESSED.getStatus()));
        int workOrderUpdateCount = otbWorkorderService.updateBatch(workorderList);
        Validate.isTrue(workOrderUpdateCount == workorderList.size(), "Failed to update work order");

        // workorder_prep_status Processed
        context.setWorkorderList(workorderList);
    }

    @Override
    public List<OtbPrepWorkorder> putAwayListByPrepPickingSlipId(Long prepPickingSlipId) {
        return lambdaQuery()
                .eq(OtbPrepWorkorder::getOtbPrepPickingSlipId, prepPickingSlipId)
                .in(OtbPrepWorkorder::getOtbPrepWorkorderStatus, Arrays.asList(
                        OtbPrepWorkOrderEnum.IN_PICKING.getStatus(),
                        OtbPrepWorkOrderEnum.PICKED.getStatus())
                )
                .list();
    }

    @Override
    public List<OtbPrepWorkorder> listByPrepPickingSlipIds(List<Long> prepPickingSlipIdList) {
        if (ObjectUtil.isEmpty(prepPickingSlipIdList)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(OtbPrepWorkorder::getOtbPrepPickingSlipId, prepPickingSlipIdList).list();
    }

    @Override
    public List<OtbPrepWorkorder> listByWorkOrderIdList(List<Long> idList) {

        if (ObjectUtil.isEmpty(idList)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(OtbPrepWorkorder::getOtbWorkorderId, idList).list();
    }

    @Override
    public List<OtbPrepWorkorder> listByWorkorderDetailIds(Collection<Long> longs) {
        if (ObjectUtil.isNotEmpty(longs)) {
            return lambdaQuery().in(OtbPrepWorkorder::getOtbWorkorderDetailId, longs).list();
        }
        return List.of();
    }

    /**
     * 构建预处理工单基础信息
     */
    @Override
    public OtbPrepWorkorder createBasePrepWorkorder(
            final OtbWorkorder workOrder,
            final OtbWorkorderDetail detail,
            final PrepWorkOrderTypeEnum prepWorkOrderType,
            final InventoryReserve inventoryReserve
    ) {
        OtbPrepWorkorder prepWorkorder = new OtbPrepWorkorder();
        prepWorkorder.setTransactionPartnerId(workOrder.getRequestSnapshotTransactionPartnerId());
        prepWorkorder.setOtbPrepPickingSlipId(null);
        prepWorkorder.setProductBarcode(detail.getDetailSnapshotProductBarcode());
        prepWorkorder.setProductChannelSku(detail.getDetailSnapshotProductChannelSku());
        prepWorkorder.setBinLocationId(null);
        prepWorkorder.setOtbRequestId(workOrder.getOtbRequestId());
        prepWorkorder.setOtbWorkorderId(workOrder.getId());
        prepWorkorder.setOtbWorkorderDetailId(detail.getId());
        prepWorkorder.setQty(detail.getQty());
        prepWorkorder.setOtbPrepWorkorderStatus(OtbPrepWorkOrderEnum.NEW.getStatus());
        prepWorkorder.setPrepWorkorderType(prepWorkOrderType.getStatus());
        prepWorkorder.setRefNum(getOtbPrepWorkOrderRefNum());
        prepWorkorder.setProductId(detail.getProductId());
        prepWorkorder.setPutawayQty(0);
        prepWorkorder.setInventoryReserveId(inventoryReserve.getId());
        prepWorkorder.setPrepWorkorderProductType(WorkorderProductTypeEnum.NORMAL.getStatus());

        Integer productVersionInt = null;
        switch (prepWorkOrderType) {
            case PREPPACK -> {
                productVersionInt = productSpecialService.getProductVersionInt(prepWorkorder.getProductId(), ProductConfigTypeEnum.ASSEMBLY);
            }
            case PREPMULTIBOX -> {
                productVersionInt = productSpecialService.getProductVersionInt(prepWorkorder.getProductId(), ProductConfigTypeEnum.MULTIBOX);
            }
            case PREPCONVERT -> {
                productVersionInt = productSpecialService.getProductVersionInt(prepWorkorder.getProductId(), ProductConfigTypeEnum.GROUP);
            }
            case PREPCONVERTPACK -> {
                productVersionInt = productSpecialService.getProductVersionInt(prepWorkorder.getProductId(), ProductConfigTypeEnum.ASSEMBLY);
            }
            case PREPCONVERTMULTIBOX -> {
                productVersionInt = productSpecialService.getProductVersionInt(prepWorkorder.getProductId(), ProductConfigTypeEnum.MULTIBOX);
            }
            default -> throw new IllegalStateException("Unexpected value: " + prepWorkOrderType);
        }

        prepWorkorder.setPrepWorkorderVersionInt(productVersionInt);
        prepWorkorder.setId(IdWorker.getId());

        // 复制发货类型和RI文件相关字段从工单到预工单
        prepWorkorder.setShipType(workOrder.getShipType());
        prepWorkorder.setRoutingInstructionFileFileData(workOrder.getRoutingInstructionFileFileData());
        prepWorkorder.setRoutingInstructionFileFileExtension(workOrder.getRoutingInstructionFileFileExtension());
        prepWorkorder.setRoutingInstructionFileFileType(workOrder.getRoutingInstructionFileFileType());
        prepWorkorder.setRoutingInstructionFilePaperType(workOrder.getRoutingInstructionFilePaperType());

        return prepWorkorder;
    }

    private Map<Long, List<OtbPrepWorkorder>> groupByWorkorderIdList(List<Long> workOrderIdList) {
        if (ObjectUtil.isEmpty(workOrderIdList)) {
            return Collections.emptyMap();
        }
        return lambdaQuery()
                .in(OtbPrepWorkorder::getOtbWorkorderId, workOrderIdList)
                .list()
                .stream()
                .collect(Collectors.groupingBy(OtbPrepWorkorder::getOtbWorkorderId));
    }

    private String getOtbPrepWorkOrderRefNum() {
        return FormatUtil.generateRefNum(RefNumTypeEnum.OTB_PREP_WORK_ORDER);
    }

    /**
     * 构建OTB预提工单VO对象
     *
     * @param entity OTB预提工单对象
     * @return 返回包含详细信息的OTB预提工单VO对象
     */
    private OtbPrepWorkorderVO buildOtbPrepWorkorderVO(OtbPrepWorkorder entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的OTB预提工单VO对象
        OtbPrepWorkorderVO workorderVO = Converters.get(OtbPrepWorkorderConverter.class).toVO(entity);
        // 工单
        OtbWorkorder workorder = otbWorkorderService.getById(workorderVO.getOtbWorkorderId());
        workorderVO.setOtbWorkorder(BeanUtil.copyNew(workorder, RefNumVO.class));
        // 详情
        List<OtbPrepWorkorderDetailVO> detailList = otbPrepWorkorderDetailService.listByOtbPrepWorkorderId(entity.getId());
        // 产品/库位
        workorderVO.setDetailList(detailList);
        // 产品/库位
        return workorderVO;
    }

    /**
     * 填充字段
     *
     * @param dataList 页面数据
     */
    private void fillPageField(List<OtbPrepWorkorderPageVO> dataList) {
        if (ObjectUtil.isEmpty(dataList)) {
            return;
        }
        // 工单
        List<Long> workorderIdList = StreamUtils.distinctMap(dataList, OtbPrepWorkorderPageVO::getOtbWorkorderId);
        Map<Long, OtbWorkorder> workorderMap = StreamUtils.toMap(otbWorkorderService.listByIds(workorderIdList), OtbWorkorder::getId);
        dataList.stream()
                .filter(obj -> workorderMap.containsKey(obj.getOtbWorkorderId()))
                .forEach(obj -> obj.setOtbWorkorder(
                        BeanUtil.copyNew(workorderMap.get(obj.getOtbWorkorderId()), OtbPrepWorkorderWorkorderVO.class)
                ));
        // 预定库存
        List<Long> inventoryReserveIdList = StreamUtils.distinctMap(dataList, OtbPrepWorkorderPageVO::getInventoryReserveId);
        Map<Long, InventoryReserve> inventoryReserveMap = StreamUtils.toMap(inventoryReserveService.listByIds(inventoryReserveIdList), IdModel::getId);
        dataList.stream()
                .filter(obj -> inventoryReserveMap.containsKey(obj.getInventoryReserveId()))
                .forEach(obj -> obj.setInventoryReserve(
                        BeanUtil.copyNew(inventoryReserveMap.get(obj.getInventoryReserveId()), RefNumVO.class)
                ));
    }

    /**
     * 根据库位查询条件，修正查询条件
     *
     * @param query 列表查询条件
     */
    private boolean fixQueryByBinLocation(OtbPrepWorkOrderListQuery query) {
        boolean canBinLocationQuery = checkAndFixBinLocationQuery(query);
        // 未开启直接返回
        if (!canBinLocationQuery) {
            return false;
        }
        // 开启工单库存校验
        List<OtbPrepWorkorder> dataList = mapper.filterBuild(query.getOtbPrepWorkorderQuery(), query.getBinLocationQuery());
        // 校验库存
        List<Long> hasStockWorkOrderIdList = this.listCheckWorkOrderInStock(dataList, query.getBinLocationQuery());
        if (ObjectUtil.isEmpty(hasStockWorkOrderIdList)) {
            return true;
        }
        // 清空查询条件 使用工单id查询
        query.setBinLocationQuery(null);
        query.setOtbPrepWorkorderQuery(null);
        OtbPrepWorkorderQuery fixWorkOrderQuery = new OtbPrepWorkorderQuery();
        fixWorkOrderQuery.setIdList(hasStockWorkOrderIdList);
        query.setOtbPrepWorkorderQuery(fixWorkOrderQuery);

        return false;
    }

    /**
     * 校验工单库存
     *
     * @param dataList 工单集合
     * @return 有库存的工单
     */
    private List<Long> listCheckWorkOrderInStock(List<OtbPrepWorkorder> dataList, BaseBinLocationQuery binLocationQuery) {
        // 校验库存
        List<Long> workOrderIdList = StreamUtils.distinctMap(dataList, OtbPrepWorkorder::getId);
        // 详情按工单分组
        Map<Long, List<OtbPrepWorkorderDetail>> detailGroupByWorkOrderMap = otbPrepWorkorderDetailService.groupByOtbPrepWorkOrderIdList(workOrderIdList)
                .values()
                .stream()
                .flatMap(Collection::stream)
                .filter(obj -> Objects.equals(obj.getPrepWorkorderDetailType(), PrepWorkOrderDetailTypeEnum.NONE.getStatus()))
                .collect(Collectors.groupingBy(OtbPrepWorkorderDetail::getOtbPrepWorkorderId));

        return pickingSlipService.filterHasInStockWorkOrder(dataList, detailGroupByWorkOrderMap, binLocationQuery);

    }

    /**
     * 执行Begin逻辑
     *
     * @param prepWorkorderList 工单列表
     */
    private void doBegin(List<OtbPrepWorkorder> prepWorkorderList) {
        if (ObjectUtil.isEmpty(prepWorkorderList)) {
            return;
        }
        // 状态校验
        List<String> prepNotBeginRefNumList = prepWorkorderList
                .stream()
                .filter(item -> ObjectUtil.notEqual(item.getOtbPrepWorkorderStatus(), OtbPrepWorkOrderEnum.NEW.getStatus()))
                .map(OtbPrepWorkorder::getRefNum)
                .toList();
        // 判断是否包含非New状态工单数据
        Validate.isTrue(ObjectUtil.isEmpty(prepNotBeginRefNumList),
                StringUtils.join(prepNotBeginRefNumList, StringPool.COMMA).concat(" status is not New Can not Begin")
        );

        // 设置Begin
        prepWorkorderList.forEach(item -> item.setOtbPrepWorkorderStatus(OtbPrepWorkOrderEnum.BEGIN.getStatus()));

        // 更新
        Validate.isTrue(super.updateBatch(prepWorkorderList) == prepWorkorderList.size(), "Failed to update PrepWorkOrder Begin status");

        // Prep工单 Begin日志
        OtbPrepWorkorderAuditLogHelper.recordLog(prepWorkorderList);
    }


    /**
     * 获取可以拣货的工单
     *
     * @param prepPickingSlip Prep拣货单
     * @return /
     */
    @NotNull
    private Map<Long, OtbPrepWorkorder> findCanPickListByPickingSlip(OtbPrepPickingSlip prepPickingSlip) {
        return lambdaQuery().eq(OtbPrepWorkorder::getOtbPrepPickingSlipId, prepPickingSlip.getId())
                .eq(OtbPrepWorkorder::getOtbPrepWorkorderStatus, OtbPrepWorkOrderEnum.IN_PICKING.getStatus())
                .list()
                .stream()
                .collect(Collectors.toMap(IdModel::getId, Function.identity()));
    }

    /**
     * 更新拣货数量
     *
     * @param prepWorkOrderMap                  Prep工单
     * @param prepWorkOrderDetailList           Prep工单详情
     * @param prepWorkOrderDetailPickUpdateList Prep工单详情拣货信息
     */
    private void updatePickedQtyAndPicked(Map<Long, OtbPrepWorkorder> prepWorkOrderMap,
                                          List<OtbPrepWorkorderDetail> prepWorkOrderDetailList,
                                          List<OtbPrepWorkorderDetailPickVO> prepWorkOrderDetailPickUpdateList) {

        // 增加otb_prep_workorder_bin_location、locked
        List<OtbPrepWorkorderBinLocation> prepWorkOrderBinLocationList = prepWorkOrderDetailPickUpdateList.stream()
                .map(obj -> {
                    OtbPrepWorkorderBinLocation wkBinLocation = BeanUtil.copyNew(obj, OtbPrepWorkorderBinLocation.class);
                    wkBinLocation.setId(IdWorker.getId());
                    wkBinLocation.setOtbPrepWorkorderDetailId(obj.getId());
                    obj.setOtbPrepWorkorderBinLocationId(wkBinLocation.getId());
                    // 分配至库位的数量
                    wkBinLocation.setQty(obj.getChangePickQty());
                    // readyToGo lock_id
                    wkBinLocation.setBinLocationDetailLockedId(obj.getReadyToGoLocked().getId());
                    return wkBinLocation;
                })
                .toList();
        otbPrepWorkorderBinLocationService.insertBatch(prepWorkOrderBinLocationList);

        // 需要更新的工单详情
        Map<Long, Integer> detailPickChangeMap = prepWorkOrderDetailPickUpdateList.stream()
                .collect(Collectors.groupingBy(OtbPrepWorkorderDetailPickVO::getId,
                        Collectors.mapping(BasePickVO::getChangePickQty, Collectors.summingInt(Integer::intValue)))
                );
        List<OtbPrepWorkorderDetail> pickDetailUpdateList = prepWorkOrderDetailList.stream()
                .filter(obj -> detailPickChangeMap.containsKey(obj.getId()))
                .toList();

        // 更新拣货数量
        List<OtbPrepWorkorderDetail> detailUpdateList = pickingSlipService.updateDetailPickQty(prepWorkOrderDetailList, pickDetailUpdateList);
        // 更新工单详情
        int wkDetailUpdateCount = otbPrepWorkorderDetailService.updateBatch(detailUpdateList);
        Validate.isTrue(wkDetailUpdateCount == detailUpdateList.size(), "Failed to update work order detail");

        // 更新工单状态
        List<OtbPrepWorkorder> updateWorkorderList = prepWorkOrderDetailList.stream()
                .collect(Collectors.groupingBy(OtbPrepWorkorderDetail::getOtbPrepWorkorderId))
                .entrySet()
                .stream()
                .map(entry -> {
                    // 全部拣货完
                    List<OtbPrepWorkorderDetail> detailList = entry.getValue();
                    boolean allPicked = detailList.stream()
                            .allMatch(obj -> Objects.equals(obj.getPickedQty(), obj.getQty()))
                            || ObjectUtil.isEmpty(detailList);
                    OtbPrepWorkorder workorder = prepWorkOrderMap.get(entry.getKey());
                    // 更新状态
                    workorder.setOtbPrepWorkorderStatus(allPicked
                            ? OtcPrepWorkorderStatusEnum.PICKED.getStatus()
                            : workorder.getOtbPrepWorkorderStatus()
                    );
                    return workorder;
                })
                .filter(obj -> Objects.equals(obj.getOtbPrepWorkorderStatus(), OtcPrepWorkorderStatusEnum.PICKED.getStatus()))
                .toList();

        Validate.isTrue(super.updateBatch(updateWorkorderList) == updateWorkorderList.size(),
                "Failed to update PrepWorkorder FinishPicking status"
        );

        // Prep工单: InPicking -> Picked 日志
        for (OtbPrepWorkorder prepWorkorder : updateWorkorderList) {
            OtbPrepWorkorderAuditLogHelper.recordLog(prepWorkorder, WorkorderLogConstant.PICKED_DESCRIPTION, null);
        }
    }

}
