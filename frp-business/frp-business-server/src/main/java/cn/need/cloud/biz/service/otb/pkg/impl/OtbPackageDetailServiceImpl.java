package cn.need.cloud.biz.service.otb.pkg.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.otb.OtbPackageStatusEnum;
import cn.need.cloud.biz.converter.otb.OtbPackageDetailConverter;
import cn.need.cloud.biz.mapper.otb.OtbPackageDetailMapper;
import cn.need.cloud.biz.model.bo.otb.OtbBuildPackageContextBo;
import cn.need.cloud.biz.model.entity.otb.OtbPackage;
import cn.need.cloud.biz.model.entity.otb.OtbPackageDetail;
import cn.need.cloud.biz.model.param.otb.create.pkg.OtbPackageCreateParam;
import cn.need.cloud.biz.model.query.otb.pkg.OtbPackageDetailQuery;
import cn.need.cloud.biz.model.vo.base.BasePackageVO;
import cn.need.cloud.biz.model.vo.otb.pkg.OtbPackageDetailVO;
import cn.need.cloud.biz.model.vo.page.OtbPackageDetailPageVO;
import cn.need.cloud.biz.service.otb.pkg.OtbPackageDetailService;
import cn.need.cloud.upms.client.api.TenantClient;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * OTB包裹详情 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtbPackageDetailServiceImpl extends SuperServiceImpl<OtbPackageDetailMapper, OtbPackageDetail> implements OtbPackageDetailService {

    @Resource
    private TenantClient tenantClient;

    @Override
    public List<OtbPackageDetailPageVO> listByQuery(OtbPackageDetailQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<OtbPackageDetailPageVO> pageByQuery(PageSearch<OtbPackageDetailQuery> search) {
        Page<OtbPackageDetail> page = Conditions.page(search, entityClass);
        List<OtbPackageDetailPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public OtbPackageDetailVO detailById(Long id) {
        OtbPackageDetail entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in OtbPackageDetail");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "OtbPackageDetail", id));
        }
        return buildOtbPackageDetailVO(entity);
    }


    @Override
    public List<OtbPackageDetailVO> listByOtbPackageId(Long otbPackageId) {
        List<OtbPackageDetail> list = lambdaQuery().eq(OtbPackageDetail::getOtbPackageId, otbPackageId).list();
        return Converters.get(OtbPackageDetailConverter.class).toVO(list);
    }

    @Override
    public void generateDetail(OtbBuildPackageContextBo contextBo) {
        //获取前端传参
        OtbPackageCreateParam param = contextBo.getParam();
        //获取包裹列表
        List<OtbPackage> otbPackageList = contextBo.getOtbPackageList();
        //满箱信息
        List<BasePackageVO> fullPackageDetailList = param.getFullPackageDetailList();
        //半箱信息
        List<BasePackageVO> tailPackageDetailList = param.getTailPackageDetailList();
        //遍历包裹集合
        List<OtbPackageDetail> list = otbPackageList.stream().map(item -> {
            //otb详情对象
            OtbPackageDetail otbPackageDetail = new OtbPackageDetail();
            //填充包裹id
            otbPackageDetail.setOtbPackageId(item.getId());
            //满箱
            if (StringUtil.equals(item.getOtbPackageType(), OtbPackageStatusEnum.FULL.getCode())) {
                fillPackageDetail(fullPackageDetailList, otbPackageDetail);
            }
            //半箱
            if (StringUtil.equals(item.getOtbPackageType(), OtbPackageStatusEnum.TAIL.getCode())) {
                fillPackageDetail(tailPackageDetailList, otbPackageDetail);
            }
            return otbPackageDetail;
        }).toList();
        //持久化包裹详情
        super.insertBatch(list);
        //加载到上下文
        contextBo.setOtbPackageDetailList(list);
    }

    @Override
    public List<OtbPackageDetail> listByOtbPackageId(List<Long> otbPackageIdList) {
        //判空
        if (ObjectUtil.isEmpty(otbPackageIdList)) {
            return Lists.arrayList();
        }
        //返回包裹详情
        return lambdaQuery()
                .in(OtbPackageDetail::getOtbPackageId, otbPackageIdList)
                .list();
    }

    @Override
    public List<OtbPackageDetailVO> listByOtbPackageId(Set<Long> idList) {
        if (ObjectUtil.isEmpty(idList)) {
            return Lists.arrayList();
        }
        List<OtbPackageDetail> list = lambdaQuery().in(OtbPackageDetail::getOtbPackageId, idList).list();
        return BeanUtil.copyNew(list, OtbPackageDetailVO.class);
    }

    /**
     * 填充包裹详情
     *
     * @param tailPackageDetailList 半箱包裹详情
     * @param otbPackageDetail      包裹详情
     */
    private void fillPackageDetail(List<BasePackageVO> tailPackageDetailList, OtbPackageDetail otbPackageDetail) {
        BasePackageVO basePackageVO = tailPackageDetailList.get(0);
        otbPackageDetail.setProductBarcode(basePackageVO.getProductBarcode());
        otbPackageDetail.setProductChannelSku(basePackageVO.getProductChannelSku());
        otbPackageDetail.setProductId(basePackageVO.getProductId());
        otbPackageDetail.setQty(basePackageVO.getQty());
        otbPackageDetail.setLineNum(1);
    }

    /**
     * 构建OTB包裹详情VO对象
     *
     * @param entity OTB包裹详情对象
     * @return 返回包含详细信息的OTB包裹详情VO对象
     */
    private OtbPackageDetailVO buildOtbPackageDetailVO(OtbPackageDetail entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的OTB包裹详情VO对象
        return Converters.get(OtbPackageDetailConverter.class).toVO(entity);
    }

}
