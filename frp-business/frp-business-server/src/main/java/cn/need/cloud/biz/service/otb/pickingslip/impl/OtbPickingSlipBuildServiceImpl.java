package cn.need.cloud.biz.service.otb.pickingslip.impl;

import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.base.PrintStatusEnum;
import cn.need.cloud.biz.client.constant.enums.base.ReLabelStatus;
import cn.need.cloud.biz.client.constant.enums.inventory.InventoryLockedStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otb.OtbPickingSlipStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otb.OtbWorkorderEnum;
import cn.need.cloud.biz.client.constant.workorder.WorkorderLogConstant;
import cn.need.cloud.biz.model.bo.base.BaseModelInventoryReserveBO;
import cn.need.cloud.biz.model.entity.binlocation.BinLocation;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetailLocked;
import cn.need.cloud.biz.model.entity.otb.*;
import cn.need.cloud.biz.model.query.otb.pickingslip.OtbPickingSlipFilterBuildQuery;
import cn.need.cloud.biz.model.query.otb.workorder.OtbWorkOrderListQuery;
import cn.need.cloud.biz.model.vo.base.workorder.WorkOrderNoEnoughAvailQtyVO;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbFilterBuildContextVO;
import cn.need.cloud.biz.service.base.PickingSlipService;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailLockedService;
import cn.need.cloud.biz.service.binlocation.BinLocationService;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.OtbPickingSlipAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.OtbWorkorderAuditLogHelper;
import cn.need.cloud.biz.service.inventory.InventoryLockedService;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPickingSlipBuildService;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPickingSlipDetailService;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPickingSlipService;
import cn.need.cloud.biz.service.otb.workorder.OtbPrepWorkorderService;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderDetailService;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderService;
import cn.need.cloud.biz.util.BinLocationCheckUtil;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.biz.util.ProductCacheUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.core.session.Users;
import cn.need.framework.common.mybatis.model.IdModel;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <p>
 * otb拣货单 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
@AllArgsConstructor
public class OtbPickingSlipBuildServiceImpl implements OtbPickingSlipBuildService {

    private final OtbWorkorderService otbWorkorderService;

    private final OtbPickingSlipDetailService otbPickingSlipDetailService;

    private final OtbWorkorderDetailService otbWorkorderDetailService;

    private final OtbPrepWorkorderService otbPrepWorkorderService;

    private final PickingSlipService pickingSlipService;

    private final BinLocationService binLocationService;

    private final BinLocationDetailLockedService binLocationDetailLockedService;

    private final InventoryLockedService inventoryLockedService;

    private final OtbPickingSlipService otbPickingSlipService;

    /**
     * 聚合拣货单详情
     *
     * @param pickingSlipDetailList 拣货单详情
     * @return /
     */
    private static List<OtbPickingSlipDetail> aggregateDetailList(List<OtbPickingSlipDetail> pickingSlipDetailList) {

        if (ObjectUtil.isEmpty(pickingSlipDetailList)) {
            return Collections.emptyList();
        }

        return pickingSlipDetailList.stream()
                .collect(Collectors.groupingBy(obj -> String.format("%s:%s:%s:%s:%s",
                        obj.getOtbPickingSlipId(), obj.getBinLocationDetailId(),
                        obj.getProductId(), obj.getProductBarcode(), obj.getProductChannelSku())
                ))
                .values()
                .stream()
                .map(details -> {
                    // 聚合分组的数据
                    OtbPickingSlipDetail first = details.get(0);
                    OtbPickingSlipDetail merge = new OtbPickingSlipDetail();
                    // 复制非聚合属性
                    BeanUtil.copy(first, merge);
                    merge.setId(IdWorker.getId());
                    merge.setQty(details.stream().mapToInt(OtbPickingSlipDetail::getQty).sum());
                    return merge;
                })
                // 拣货单分组
                .collect(Collectors.groupingBy(OtbPickingSlipDetail::getOtbPickingSlipId))
                .values()
                .stream()
                // Barcode排序
                .peek(details -> details.sort(Comparator.comparing(OtbPickingSlipDetail::getProductBarcode)))
                // 设置LineNum
                .peek(details -> IntStream.range(0, details.size()).forEach(idx -> details.get(idx).setLineNum(idx + 1)))
                .flatMap(Collection::stream)
                .toList();
    }

    /// ///////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<WorkOrderNoEnoughAvailQtyVO> filterBuild(OtbPickingSlipFilterBuildQuery query) {
        // 上下文信息
        OtbFilterBuildContextVO context = new OtbFilterBuildContextVO();
        context.setQuery(query);

        // 检查并返回工单列表
        this.checkAndGetWorkOrderList(context);

        // 检查工单库存
        this.checkWorkOrderInStock(context);

        // 工单详情为空，直接返回
        if (ObjectUtil.isEmpty(context.getWorkOrderList())) {
            return context.getNoStockWorkOrderList();
        }

        // 构建拣货单
        this.buildPickingSlipList(context);

        // 构建拣货单详情
        this.buildPickingSlipDetailList(context);

        // 锁库位库存
        this.lockBinLocation(context);

        List<OtbWorkorder> workOrderList = context.getWorkOrderList();
        List<Long> workOrderIdList = StreamUtils.distinctMap(workOrderList, OtbWorkorder::getId);

        // 释放WorkOrder LockedInventory
        inventoryLockedService.releaseLockedInventory(otbWorkorderDetailService.findInventoryReleaseLockedParam(workOrderIdList));
        this.releaseReserveLocked(context);

        // 批量入库拣货单
        otbPickingSlipService.insertBatch(context.getPickingSlipList());
        // 批量入库拣货单详情
        otbPickingSlipDetailService.insertBatch(context.getPickingSlipDetailList());

        // 更新 WorkOrder 拣货单id 和 FinishBuildPickingSlip
        Validate.isTrue(otbWorkorderService.updateBatch(workOrderList) == workOrderList.size(),
                "Update WorkOrder status [FinishBuildPickingSlip] failed"
        );

        // 记录日志
        this.recordFilterBuildLog(context);
        return context.getNoStockWorkOrderList();
    }

    /**
     * 锁定库位
     *
     * @param context 上下文
     */
    private void lockBinLocation(OtbFilterBuildContextVO context) {
        // 拣货单映射
        Map<Long, OtbPickingSlip> pickingSlipMap = StreamUtils.toMap(context.getPickingSlipList(), IdModel::getId);
        // 构建锁定库位库存
        List<OtbPickingSlipDetail> pickingSlipDetailList = context.getPickingSlipDetailList();
        List<BinLocationDetailLocked> lockedList = pickingSlipDetailList.stream()
                .map(obj -> {
                    // 构建锁定 库位详情实体对象
                    BinLocationDetailLocked locked = new BinLocationDetailLocked();
                    locked.setId(IdWorker.getId());
                    locked.setBinLocationDetailId(obj.getBinLocationDetailId());
                    locked.setBinLocationId(obj.getBinLocationId());
                    locked.setProductId(obj.getProductId());
                    locked.setProductVersionId(obj.getProductVersionId());
                    locked.setQty(obj.getQty());
                    locked.setFinishQty(0);
                    locked.setLockedStatus(InventoryLockedStatusEnum.LOCKED.getStatus());
                    locked.setRefTableId(obj.getId());
                    locked.setRefTableName(OtbPickingSlipDetail.class.getSimpleName());
                    locked.setRefTableRefNum(String.valueOf(obj.getLineNum()));
                    locked.setRefTableShowName(OtbPickingSlip.class.getSimpleName());
                    Optional.ofNullable(pickingSlipMap.get(obj.getOtbPickingSlipId()))
                            .ifPresent(slip -> locked.setRefTableShowRefNum(slip.getRefNum()));
                    return locked;
                })
                .toList();

        // 校验库位是否禁用
        BinLocationCheckUtil.checkStatuses(lockedList);

        pickingSlipService.releaseWorkorderVirtualLocked(context.getLockedGroupByWkDetailMap());

        // 锁定库位库存
        binLocationDetailLockedService.lockedBinLocationInventory(lockedList);

        // 填充库位锁信息
        Map<Long, Long> lockedIdMap = lockedList.stream()
                .collect(Collectors.toMap(BinLocationDetailLocked::getRefTableId, IdModel::getId));
        pickingSlipDetailList.forEach(obj -> obj.setBinLocationDetailLockedId(lockedIdMap.get(obj.getId())));
    }

    /**
     * 构建拣货单详情
     */
    private void buildPickingSlipDetailList(OtbFilterBuildContextVO context) {
        Map<Long, List<OtbWorkorderDetail>> workOrderDetailGroupByWorkOrderIdMap = context.getWorkOrderDetailGroupByWorkOrderIdMap();
        List<OtbWorkorder> workOrderList = context.getWorkOrderList();

        // 虚拟库位
        context.setVirtualBinLocationMap(binLocationService.allVirtualBinLocationList());

        // 工单集合 -> 工单详情集合 -> 产品id集合 -> 库位详情集合 层层嵌套
        List<OtbPickingSlipDetail> pickingSlipDetailList = workOrderList
                .stream()
                .filter(wk -> workOrderDetailGroupByWorkOrderIdMap.containsKey(wk.getId()))
                // 工单
                .map(wk -> workOrderDetailGroupByWorkOrderIdMap.get(wk.getId())
                        .stream()
                        // 工单详情
                        .map(wkDetail -> this.doBuildPickingSlipDetail(context, wk, wkDetail))
                        .toList())
                .flatMap(Collection::stream)
                .flatMap(Collection::stream)
                .toList();
        // 聚合
        List<OtbPickingSlipDetail> mergeDetailList = aggregateDetailList(pickingSlipDetailList);

        // 赋值拣货单详情、工单库位详情到上下文中
        context.setPickingSlipDetailList(mergeDetailList);
    }

    /**
     * 执行构建Prep和正常构建拣货单详情逻辑
     *
     * @param context  上下文
     * @param wk       工单
     * @param wkDetail 工单详情
     * @return /
     */
    @NotNull
    private List<OtbPickingSlipDetail> doBuildPickingSlipDetail(OtbFilterBuildContextVO context,
                                                                OtbWorkorder wk,
                                                                OtbWorkorderDetail wkDetail) {

        // 虚拟库位
        Map<Long, BinLocation> virtualBinLocationMap = context.getVirtualBinLocationMap();
        // 当前工单详情与Prep工单的映射
        List<OtbPrepWorkorder> currentPrepWorkOrderList = context.getPrepWorkOrderGroupByWkDetailMap()
                .getOrDefault(wkDetail.getId(), Collections.emptyList());

        // 需要从Prep 拣货的数量
        int needPickFromPreBinLocationQty = currentPrepWorkOrderList.stream()
                .map(OtbPrepWorkorder::getPutawayQty)
                .mapToInt(Integer::intValue)
                .sum();
        // 当前Prep工单工作的库位
        List<Long> currentPrepBinLocationIdList = currentPrepWorkOrderList.stream()
                .map(OtbPrepWorkorder::getBinLocationId)
                .distinct()
                .toList();

        // 总数
        AtomicInteger totalQty = new AtomicInteger(wkDetail.getQty());
        // 可以直接取的数量
        AtomicInteger directPickBinLocationQty = new AtomicInteger(Math.max(totalQty.get() - needPickFromPreBinLocationQty, 0));
        AtomicInteger prepPickBinLocationQty = new AtomicInteger(Math.max(totalQty.get() - directPickBinLocationQty.get(), 0));

        // 构建拣货单详情
        List<OtbPickingSlipDetail> psDetailList = context.getRealAvailableBinLocationDetailGroupByProductMap()
                .getOrDefault(wkDetail.getProductId(), Collections.emptyList())
                .stream()
                // 还有剩余数量未分配
                .filter(binDetail -> totalQty.get() > 0 && binDetail.getInStockQty() > 0)
                // 库位详情
                .map(binDetail -> {
                    // 当前需要分配数量
                    int currentQty;
                    // 虚拟库位拣货
                    boolean isVirtualBinLocation = virtualBinLocationMap.containsKey(binDetail.getBinLocationId());
                    boolean isPrepPick = isVirtualBinLocation
                            && currentPrepBinLocationIdList.contains(binDetail.getBinLocationId());
                    // Prep拣货
                    if (isPrepPick) {
                        currentQty = Math.min(totalQty.get(), binDetail.getInStockQty());
                        prepPickBinLocationQty.addAndGet(-currentQty);
                        // 正常库位
                    } else if (!isVirtualBinLocation && directPickBinLocationQty.get() > 0) {
                        currentQty = Math.min(totalQty.get(), binDetail.getInStockQty());
                        directPickBinLocationQty.addAndGet(-currentQty);
                    } else {
                        // 虚拟库位不需要分配
                        return null;
                    }
                    // 构建拣货单详情
                    OtbPickingSlipDetail detail = this.buildPickingSlipDetail(wk, wkDetail, binDetail);
                    // 设置数量
                    detail.setQty(currentQty);
                    // 扣除库位上的库存
                    binDetail.setInStockQty(binDetail.getInStockQty() - currentQty);
                    // 剩余数量
                    totalQty.addAndGet(-currentQty);
                    return detail;
                })
                .filter(Objects::nonNull)
                .toList();
        // 库存不够
        Validate.isTrue(totalQty.get() == 0, String.format(
                "WorkOrder: %s build PickingSlip details Product: %s Less InStock %d",
                wk.getRefNum(),
                Optional.ofNullable(ProductCacheUtil.getById(wkDetail.getProductId()))
                        .map(pro -> String.format("%s/%s", pro.getUpc(), pro.getSupplierSku()))
                        .orElse(String.valueOf(wkDetail.getProductId())),
                totalQty.get())
        );
        return psDetailList;
    }

    /**
     * 构建拣货单详情
     *
     * @param workOrder         工单
     * @param workOrderDetail   工单详情
     * @param binLocationDetail 库位详情
     * @return 拣货单详情
     */
    private OtbPickingSlipDetail buildPickingSlipDetail(OtbWorkorder workOrder,
                                                        OtbWorkorderDetail workOrderDetail,
                                                        BinLocationDetail binLocationDetail) {
        OtbPickingSlipDetail slipDetail = new OtbPickingSlipDetail();
        BeanUtil.copy(workOrderDetail, slipDetail);

        // 手动设置id
        slipDetail.setId(IdWorker.getId());
        slipDetail.setLineNum(0);
        // 拣货单id
        slipDetail.setOtbPickingSlipId(workOrder.getOtbPickingSlipId());
        // 库位信息
        slipDetail.setBinLocationDetailId(binLocationDetail.getId());
        slipDetail.setBinLocationId(binLocationDetail.getBinLocationId());
        // 产品信息
        slipDetail.setProductVersionId(binLocationDetail.getProductVersionId());
        slipDetail.setProductId(binLocationDetail.getProductId());

        // 产品信息
        slipDetail.setProductBarcode(workOrderDetail.getDetailSnapshotProductBarcode());
        slipDetail.setProductChannelSku(workOrderDetail.getDetailSnapshotProductChannelSku());

        // 数量赋值
        slipDetail.setPackedQty(0);
        // 拣货数量
        slipDetail.setPickedQty(0);
        slipDetail.setRelabelStatus(ReLabelStatus.NONE.getStatus());
        return slipDetail;
    }

    /**
     * 构建拣货单
     *
     * @param context 上下文
     */
    private void buildPickingSlipList(OtbFilterBuildContextVO context) {
        List<OtbWorkorder> workOrderList = context.getWorkOrderList();
        OtbPickingSlipFilterBuildQuery query = context.getQuery();
        List<OtbPickingSlip> pickingSlipList = workOrderList.stream()
                .map(wk -> {
                    OtbPickingSlip pickingSlip = new OtbPickingSlip();
                    pickingSlip.setId(IdWorker.getId());
                    pickingSlip.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.OTB_PICKING_SLIP.getCode()));
                    pickingSlip.setOtbPickingSlipStatus(OtbPickingSlipStatusEnum.NEW.getStatus());
                    pickingSlip.setPrintStatus(PrintStatusEnum.NONE.getStatus());
                    pickingSlip.setPickToStation(query.getPickToStation());
                    pickingSlip.setNote(query.getNote());
                    pickingSlip.setOtbPickSlipType(wk.getOtbWorkorderType());
                    pickingSlip.setAssignedUserId(Objects.requireNonNull(Users.getUser()).getId());
                    pickingSlip.setOtbWorkorderId(wk.getId());
                    pickingSlip.setShipType(wk.getShipType());
                    pickingSlip.setRoutingInstructionFileFileData(wk.getRoutingInstructionFileFileData());
                    pickingSlip.setRoutingInstructionFileFileExtension(wk.getRoutingInstructionFileFileExtension());
                    pickingSlip.setRoutingInstructionFileFileType(wk.getRoutingInstructionFileFileType());
                    pickingSlip.setRoutingInstructionFilePaperType(wk.getRoutingInstructionFilePaperType());

                    // 拣货单id设置回去
                    wk.setOtbPickingSlipId(pickingSlip.getId());
                    wk.setOtbWorkorderStatus(OtbWorkorderEnum.IN_PICKING.getStatus());
                    return pickingSlip;
                })
                .toList();

        // 设置构建来源类型
        pickingSlipList.forEach(pickingSlip -> pickingSlip.setBuildFromType(context.getQuery().getBuildFromType().getType()));
        // 赋值拣货单信息到上下文中
        context.setPickingSlipList(pickingSlipList);
    }


    /**
     * 检查并获取工单列表
     *
     * @param context 上下文
     */
    private void checkAndGetWorkOrderList(OtbFilterBuildContextVO context) {
        OtbPickingSlipFilterBuildQuery query = context.getQuery();

        List<OtbWorkorder> workOrderList = otbWorkorderService.filterBuildByQuery(query);
        if (ObjectUtil.isEmpty(workOrderList)) {
            throw new BusinessException("Current Filter has No WorkOrders to Build!");
        }
        List<Long> workOrderIdList = StreamUtils.distinctMap(workOrderList, OtbWorkorder::getId);
        // 获取工单详情
        Map<Long, List<OtbWorkorderDetail>> workOrderDetailGroupByWorkOrderIdMap = otbWorkorderDetailService.groupByOtbWorkOrderIdList(workOrderIdList);
        // 让详情数据按照工单顺序一致
        context.setWorkOrderDetailGroupByWorkOrderIdMap(StreamUtils.sortByList(workOrderDetailGroupByWorkOrderIdMap, workOrderList));
        context.setWorkOrderList(new ArrayList<>(workOrderList));

        // Prep工单虚拟库位拣货
        List<OtbPrepWorkorder> prepWorkorderList = otbPrepWorkorderService.listByOtbWorkOrderIdList(workOrderIdList);
        // 绑定到上下文中
        context.setPrepWorkOrderGroupByWkDetailMap(StreamUtils.groupBy(prepWorkorderList, OtbPrepWorkorder::getOtbWorkorderDetailId));
    }


    /**
     * 检查工单库存
     *
     * @param context 上下文
     */
    private void checkWorkOrderInStock(OtbFilterBuildContextVO context) {
        // 工单详情映射
        Map<Long, List<OtbWorkorderDetail>> workOrderDetailGroupByWorkOrderIdMap = context.getWorkOrderDetailGroupByWorkOrderIdMap();

        // 工单映射
        Map<Long, OtbWorkorder> workOrderRefNumMap = context.getWorkOrderList()
                .stream()
                .collect(Collectors.toMap(OtbWorkorder::getId, Function.identity()));

        // 处理Prep绑定的虚拟库位
        this.dealWithVirtualBinLocation(context);

        // 获取不足库存的工单
        List<WorkOrderNoEnoughAvailQtyVO> noEnoughList = pickingSlipService.findNoEnoughList(
                workOrderDetailGroupByWorkOrderIdMap,
                context.getRealAvailableBinLocationDetailGroupByProductMap(),
                productStockMap -> StreamUtils.filterHasProductStock(workOrderDetailGroupByWorkOrderIdMap, productStockMap),
                (noEnough, detail) -> {
                    noEnough.setRefNum(workOrderRefNumMap.get(detail.getOtbWorkorderId()).getRefNum());
                    noEnough.setId(detail.getOtbWorkorderId());
                }
        );

        // 设置不足库存的工单到上下文
        context.setNoStockWorkOrderList(noEnoughList);
        // 删除库存不足的工单
        List<Long> noEnoughIdList = StreamUtils.distinctMap(noEnoughList, WorkOrderNoEnoughAvailQtyVO::getId);
        context.getWorkOrderList().removeIf(obj -> noEnoughIdList.contains(obj.getId()));
    }

    /**
     * 处理Prep绑定的虚拟库位
     *
     * @param context 上下文
     */
    private void dealWithVirtualBinLocation(OtbFilterBuildContextVO context) {
        Map<Long, List<OtbWorkorderDetail>> workOrderDetailGroupByWorkOrderIdMap = context.getWorkOrderDetailGroupByWorkOrderIdMap();
        // 获取产品id集合
        List<Long> productIdList = workOrderDetailGroupByWorkOrderIdMap.values()
                .stream()
                .flatMap(Collection::stream)
                .map(OtbWorkorderDetail::getProductId)
                .toList();
        // 虚拟库位获取
        List<Long> workorderDetailIdList = workOrderDetailGroupByWorkOrderIdMap.values()
                .stream()
                .flatMap(Collection::stream)
                .map(IdModel::getId)
                .distinct()
                .toList();
        // 获取锁住的库位详情
        Map<Long, List<BinLocationDetailLocked>> lockedGroupByWkDetailMap = binLocationDetailLockedService.groupByRefTableIdLocked(workorderDetailIdList);

        // 绑定可用库位
        context.setRealAvailableBinLocationDetailGroupByProductMap(pickingSlipService.fullCanBuildVirtualBinLocation(
                Optional.ofNullable(context.getQuery())
                        .map(OtbPickingSlipFilterBuildQuery::getFilter)
                        .map(OtbWorkOrderListQuery::getBinLocationQuery)
                        .orElse(null),
                productIdList,
                lockedGroupByWkDetailMap)
        );
        context.setLockedGroupByWkDetailMap(lockedGroupByWkDetailMap);

    }

    /**
     * 释放预定锁
     *
     * @param context 上下文
     */
    private void releaseReserveLocked(OtbFilterBuildContextVO context) {
        List<OtbPrepWorkorder> prepWorkorderList = context.getPrepWorkOrderGroupByWkDetailMap().values()
                .stream()
                .flatMap(Collection::stream)
                .toList();
        if (ObjectUtil.isEmpty(prepWorkorderList)) {
            return;
        }
        Map<Long, List<OtbPrepWorkorder>> prepGroupByDetailWkMap = prepWorkorderList
                .stream()
                .collect(Collectors.groupingBy(OtbPrepWorkorder::getOtbWorkorderDetailId));
        // 获取工单详情
        List<OtbWorkorderDetail> releaseWorkorderDetailList = context.getWorkOrderDetailGroupByWorkOrderIdMap()
                .values()
                .stream()
                .flatMap(Collection::stream)
                .filter(obj -> prepGroupByDetailWkMap.containsKey(obj.getId()))
                .toList();

        pickingSlipService.releaseReserveLocked(BeanUtil.copyNew(releaseWorkorderDetailList, BaseModelInventoryReserveBO.class));
    }

    /**
     * 记录FilterBuild日志
     *
     * @param context 上下文
     */
    private void recordFilterBuildLog(OtbFilterBuildContextVO context) {
        // 工单: New -> InPicking
        Map<Long, OtbPickingSlip> pickingSlipMap = context.getPickingSlipList()
                .stream()
                .collect(Collectors.toMap(IdModel::getId, Function.identity()));
        for (OtbWorkorder workorder : context.getWorkOrderList()) {
            OtbPickingSlip pickingSlip = pickingSlipMap.get(workorder.getOtbPickingSlipId());
            // 工单: Filter Build PickingSlip
            OtbWorkorderAuditLogHelper.recordLog(workorder, WorkorderLogConstant.FILTER_BUILD_STATUS,
                    pickingSlip.toLogBuild(), null, BaseTypeLogEnum.OPERATION.getType()
            );

            // 工单: New -> InPicking
            OtbWorkorderAuditLogHelper.recordLog(workorder, OtbWorkorderEnum.IN_PICKING.getStatus(), null, null);
        }

        // 拣货单: New  日志
        OtbPickingSlipAuditLogHelper.recordLog(context.getPickingSlipList());
    }

}
