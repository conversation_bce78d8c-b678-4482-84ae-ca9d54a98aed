package cn.need.cloud.biz.client.dto.otc;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * OTC请求包裹详情 dto对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OtcRequestPackageDetailDTO extends SuperDTO {


    /**
     * 行序号
     */
    private Integer lineNum;

    /**
     * OTC请求ID
     */
    private Long otcRequestId;

    /**
     * 数量
     */
    private Integer qty;

    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * OTC请求包裹ID
     */
    private Long otcRequestPackageId;

}