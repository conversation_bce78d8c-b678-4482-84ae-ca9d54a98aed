package cn.need.cloud.biz.client.constant.enums.base;

import cn.need.framework.common.core.lang.Validate;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2024-11-04
 */
@Getter
@AllArgsConstructor
public enum PrintStatusEnum {

    /**
     * 无
     */
    NONE("None"),
    /**
     * 失败
     */
    FAIL("Fail"),
    /**
     * 成功
     */
    SUCCESS("Success");

    @EnumValue
    @JsonValue
    private final String status;

    /**
     * 判断状态值是否合法
     *
     * @param status 状态值
     * @return 枚举类
     */
    public static boolean hasSafeStatus(String status) {
        return Arrays.stream(PrintStatusEnum.values())
                .anyMatch(e -> e.getStatus().equals(status));
    }

    /**
     * 校验状态值是否合法
     *
     * @param status 状态值
     */
    public static void checkStatus(String status) {
        Validate.isTrue(hasSafeStatus(status), "{} is not safe PrintStatus", status);
    }
}
