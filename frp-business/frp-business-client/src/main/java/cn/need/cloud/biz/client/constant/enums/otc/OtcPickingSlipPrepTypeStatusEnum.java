package cn.need.cloud.biz.client.constant.enums.otc;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * <AUTHOR>
 * @since 2024-11-04
 */
@Getter
@AllArgsConstructor
public enum OtcPickingSlipPrepTypeStatusEnum {
    /**
     *
     */
    PRE_PACK("PrePack"),
    /**
     * 无
     */
    NONE("None");
    @EnumValue
    @JsonValue
    private final String status;
}
