package cn.need.framework.starter.web.annotation;

import java.lang.annotation.*;

/**
 * 方法耗时统计注解
 * <p>
 * 用于标记需要进行耗时统计的方法。
 * 当方法被此注解标记时，AOP切面会自动记录方法的执行时间。
 * </p>
 * 
 * <AUTHOR>
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface MethodTimeLog {
    
    /**
     * 是否记录方法参数
     * 默认为false，避免敏感信息泄露
     */
    boolean logArgs() default false;
    
    /**
     * 是否记录返回值
     * 默认为false，避免大对象影响性能
     */
    boolean logResult() default false;
    
    /**
     * 慢方法阈值（毫秒）
     * 当方法执行时间超过此阈值时，会以WARN级别记录
     * 默认为1000毫秒
     */
    long slowThreshold() default 1000L;
    
    /**
     * 自定义方法描述
     * 用于在日志中显示更友好的方法描述
     */
    String description() default "";
}
