package cn.need.framework.starter.web.aspect;

import cn.need.framework.starter.web.annotation.MethodTimeLog;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.MDC;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.util.Arrays;

/**
 * 方法耗时统计切面
 * <p>
 * 该切面用于统计所有public方法的执行耗时，支持trace级别的日志输出。
 * 主要功能包括：
 * 1. 记录方法执行耗时
 * 2. 支持慢方法告警
 * 3. 可选的参数和返回值记录
 * 4. 集成TraceId链路追踪
 * </p>
 *
 * <AUTHOR>
 */
@Aspect
@Slf4j
public class MethodTimeLogAspect {

    /**
     * 定义切点：拦截所有public方法
     * 包括：
     * 1. cn.need.cloud包下的所有public方法
     * 2. 排除一些不需要统计的方法（如getter/setter等）
     */
    @Pointcut("execution(public * cn.need.framework.starter.web..*.*(..)) " +
            "&& !execution(* cn.need.framework.starter.web..*.get*(..)) " +
            "&& !execution(* cn.need.framework.starter.web..*.set*(..)) " +
            "&& !execution(* cn.need.framework.starter.web..*.is*(..)) " +
            "&& !execution(* cn.need.framework.starter.web..*.has*(..)) " +
            "&& !execution(* cn.need.framework.starter.web..*.toString(..)) " +
            "&& !execution(* cn.need.framework.starter.web..*.hashCode(..)) " +
            "&& !execution(* cn.need.framework.starter.web..*.equals(..))")
    public void publicMethodPointcut() {
        // 切点定义
    }

    /**
     * 定义切点：拦截带有@MethodTimeLog注解的方法
     */
    @Pointcut("@annotation(cn.need.framework.starter.web.annotation.MethodTimeLog) " +
            "|| @within(cn.need.framework.starter.web.annotation.MethodTimeLog)")
    public void annotatedMethodPointcut() {
        // 注解切点定义
    }

    /**
     * 环绕通知：记录方法执行耗时
     *
     * @param joinPoint 连接点
     * @return 方法执行结果
     * @throws Throwable 方法执行异常
     */
    @Around("publicMethodPointcut() || annotatedMethodPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        // 只有在TRACE级别启用时才进行统计
        if (!log.isTraceEnabled()) {
            return joinPoint.proceed();
        }

        long startTime = System.currentTimeMillis();
        String methodName = getMethodName(joinPoint);
        String traceId = MDC.get("traceId");
        String traceInfo = StringUtils.hasText(traceId) ? " [TraceId: " + traceId + "]" : "";

        // 获取方法注解配置
        MethodTimeLog annotation = getMethodTimeLogAnnotation(joinPoint);

        Object result = null;
        Throwable exception = null;

        try {
            // 记录方法开始
            if (annotation != null && annotation.logArgs()) {
                Object[] args = joinPoint.getArgs();
                log.trace("{}Method [{}] started with args: {}{}",
                        getLogPrefix(), methodName, Arrays.toString(args), traceInfo);
            } else {
                log.trace("{}Method [{}] started{}",
                        getLogPrefix(), methodName, traceInfo);
            }

            // 执行目标方法
            result = joinPoint.proceed();

            return result;

        } catch (Throwable t) {
            exception = t;
            throw t;
        } finally {
            // 计算耗时
            long duration = System.currentTimeMillis() - startTime;

            // 记录方法结束和耗时
            logMethodCompletion(methodName, duration, result, exception, annotation, traceInfo);
        }
    }

    /**
     * 获取方法名称
     */
    private String getMethodName(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String className = signature.getDeclaringType().getSimpleName();
        String methodName = signature.getName();
        return className + "." + methodName + "()";
    }

    /**
     * 获取方法上的MethodTimeLog注解
     */
    private MethodTimeLog getMethodTimeLogAnnotation(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 先检查方法上的注解
        MethodTimeLog annotation = method.getAnnotation(MethodTimeLog.class);
        if (annotation != null) {
            return annotation;
        }

        // 再检查类上的注解
        return method.getDeclaringClass().getAnnotation(MethodTimeLog.class);
    }

    /**
     * 记录方法完成信息
     */
    private void logMethodCompletion(String methodName, long duration, Object result,
                                     Throwable exception, MethodTimeLog annotation, String traceInfo) {

        // 判断是否为慢方法
        long slowThreshold = annotation != null ? annotation.slowThreshold() : 1000L;
        boolean isSlowMethod = duration > slowThreshold;

        StringBuilder logMessage = new StringBuilder();
        logMessage.append(getLogPrefix())
                .append("Method [").append(methodName).append("] ");

        if (exception != null) {
            logMessage.append("failed with exception: ")
                    .append(exception.getClass().getSimpleName())
                    .append(" - ").append(exception.getMessage());
        } else {
            logMessage.append("completed successfully");

            // 记录返回值（如果配置了）
            if (annotation != null && annotation.logResult() && result != null) {
                logMessage.append(" with result: ").append(result.toString());
            }
        }

        logMessage.append(", duration: ").append(duration).append("ms")
                .append(traceInfo);

        // 根据是否为慢方法选择日志级别
        if (isSlowMethod) {
            log.warn("{}[SLOW METHOD] {}", getLogPrefix(), logMessage.toString());
        } else {
            log.trace(logMessage.toString());
        }
    }

    /**
     * 获取日志前缀
     */
    private String getLogPrefix() {
        return "[METHOD-TIME] ";
    }
}
