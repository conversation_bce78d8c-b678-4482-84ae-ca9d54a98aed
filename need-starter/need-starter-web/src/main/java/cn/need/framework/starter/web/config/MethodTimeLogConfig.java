package cn.need.framework.starter.web.config;

import cn.need.framework.starter.web.aspect.MethodTimeLogAspect;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * 方法执行时间记录配置类
 *
 * <AUTHOR>
 */
@Configuration
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class MethodTimeLogConfig {

    /**
     * 方法耗时统计切面
     * 只有在配置启用时才创建Bean
     */
    @Bean
    @ConditionalOnProperty(name = "method.time.log.enable", havingValue = "true", matchIfMissing = false)
    public MethodTimeLogAspect methodTimeLogAspect() {
        return new MethodTimeLogAspect();
    }
}
