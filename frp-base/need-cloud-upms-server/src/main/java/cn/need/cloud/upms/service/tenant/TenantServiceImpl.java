package cn.need.cloud.upms.service.tenant;

import cn.need.cloud.dict.client.api.NumberGenerateClient;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.cloud.upms.cache.TenantCacheRepertory;
import cn.need.cloud.upms.cache.bean.TenantCache;
import cn.need.cloud.upms.client.constant.BusinessRoleType;
import cn.need.cloud.upms.client.dto.TenantQueryDTO;
import cn.need.cloud.upms.converter.tenant.TenantConverter;
import cn.need.cloud.upms.mapper.tenant.TenantMapper;
import cn.need.cloud.upms.model.entity.tenant.Tenant;
import cn.need.cloud.upms.model.param.tenant.TenantCreateParam;
import cn.need.cloud.upms.model.param.tenant.TenantUpdateParam;
import cn.need.cloud.upms.model.query.tenant.TenantQuery;
import cn.need.cloud.upms.model.vo.permissions.BusinessAndRoleVO;
import cn.need.cloud.upms.model.vo.tenant.TenantPageVO;
import cn.need.cloud.upms.model.vo.tenant.TenantVO;
import cn.need.cloud.upms.service.permissions.BusinessRoleService;
import cn.need.cloud.upms.service.user.UserTenantService;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.date.TimeMeter;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import cn.need.framework.common.support.util.ApiUtil;
import cn.need.framework.starter.tenant.core.context.TenantContextHolder;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 租户 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29
 */
@Service
public class TenantServiceImpl extends SuperServiceImpl<TenantMapper, Tenant> implements TenantService {

    @Resource
    private TenantCacheRepertory cacheRepertory;

    @Resource
    private NumberGenerateClient numberGenerateClient;

    @Resource
    private UserTenantService userTenantService;

    @Resource
    private BusinessRoleService businessRoleService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insert(Tenant entity) {
        // 生成租户编号
        String tenantCode = ApiUtil.getResultData(numberGenerateClient.generateNumber(RefNumTypeEnum.TENANT.getCode()));
        // 设置租户编号
        entity.setTenantCode(tenantCode);
        // 插入租户信息到数据库
        int insert = super.insert(entity);
        // 更新Redis缓存，以反映新插入的租户信息
        updateRedis(entity);
        // 返回影响数据条数
        return insert;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(Tenant entity) {
        // 插入租户信息到数据库
        int update = super.update(entity);
        // 更新Redis缓存，以反映新插入的租户信息
        updateRedis(getById(entity.getId()));
        // 返回影响数据条数
        return update;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int removeById(Long id) {
        // 根据租户删除 id
        int removeById = super.removeById(id);
        // 删除租户关联的用户
        userTenantService.removeByTenantId(id);
        // 删除租户缓存
        cacheRepertory.delTenant(StringUtil.toString(id));
        // 返回影响数据条数
        return removeById;
    }

    @Override
    public void initTenantCache() {
        TimeMeter meter = new TimeMeter();
        log.info(">>>>>>>>>>>>>>>>>>>>> 初始化租户数据至redis【开始】 >>>>>>>>>>>>>>>>>>>>>");
        //过滤掉失效的数据
        List<TenantCache> tenantCaches = BeanUtil.copyNew(this.list(), TenantCache.class);
        cacheRepertory.initTenant(tenantCaches);
        log.info("<<<<<<<<<<<<<<<<<<<<< 初始化租户数据至redis【完成】，总耗时：{}ms <<<<<<<<<<<<<<<<<<<<<", meter.sign());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(TenantCreateParam createParam) {

        // 检查传入租户参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException("Parameter cannot be empty");
        }

        // 获取租户转换器实例，用于将租户参数对象转换为实体对象
        TenantConverter converter = Converters.get(TenantConverter.class);

        // 将租户参数对象转换为实体对象并初始化
        Tenant entity = initTenant(converter.toEntity(createParam));

        // 插入租户实体对象到数据库
        this.insert(entity);

        // 返回租户ID
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(TenantUpdateParam updateParam) {
        // 检查传入租户参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            throw new BusinessException("Parameter cannot be empty");
        }

        // 获取租户转换器实例，用于将租户参数对象转换为实体对象
        TenantConverter converter = Converters.get(TenantConverter.class);

        // 将租户参数对象转换为实体对象
        Tenant entity = converter.toEntity(updateParam);

        // 执行更新租户操作
        return this.update(entity);

    }

    @Override
    public List<TenantPageVO> listByQuery(TenantQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<TenantPageVO> pageByQuery(PageSearch<TenantQuery> search) {
        // 根据查询条件创建分页对象
        Page<Tenant> page = Conditions.page(search, entityClass);
        // 将查询结果转换为目标页面VO列表
        List<TenantPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        // 返回包含分页数据和信息的对象
        return new PageData<>(dataList, page);
    }

    @Override
    public TenantVO detailById(Long id) {
        // 根据ID获取租户实体
        Tenant entity = getById(id);
        // 检查租户实体是否存在
        if (ObjectUtil.isEmpty(entity)) {
            // 如果实体不存在，抛出业务异常
            throw new BusinessException("id: " + id + " not found in Tenant");
        }
        // 将租户实体转换为视图对象并返回
        return buildTenantVO(entity);
    }

    @Override
    public List<TenantPageVO> listTenantByUserId(Long userId) {
        // 设置忽略租户上下文，以获取未绑定租户的权限
        TenantContextHolder.setIgnore(true);
        // 根据用户ID查询已绑定的租户列表
        return mapper.listTenantByUserId(userId);
    }

    @Override
    public List<TenantPageVO> listTenantByUserIdHasRole(Long userId) {
        // 设置忽略租户上下文，以获取未绑定租户的权限
        TenantContextHolder.setIgnore(true);
        // 根据用户ID查询已绑定的租户列表
        List<TenantPageVO> dataList = mapper.listTenantByUserId(userId);

        // 获取业务id查询角色列表
        List<Long> businessIdList = dataList.stream()
                .map(TenantPageVO::getBusinessId)
                .distinct()
                .toList();
        List<BusinessAndRoleVO> businessAndRoleList = businessRoleService.roleIdsByBusinessIdsAndType(BusinessRoleType.USER_TENANT, businessIdList);
        Map<Long, List<BusinessAndRoleVO>> businessRolesMap = businessAndRoleList.stream()
                .collect(Collectors.groupingBy(BusinessAndRoleVO::getBusinessId));

        return dataList.stream()
                .filter(tenant -> businessRolesMap.containsKey(tenant.getBusinessId()))
                // 过滤存在合作伙伴类型
                .filter(tenant -> businessRolesMap.get(tenant.getBusinessId())
                        .stream()
                        .anyMatch(role -> tenant.getPartnerType().contains(role.getRoleGroup())))
                .toList();
    }

    @Override
    public void verifyDefault(Long id) {
        // 获取指定ID的租户
        Tenant tenant = getById(id);

        // 检查租户是否存在
        if (ObjectUtil.isEmpty(tenant)) {
            throw new BusinessException("The tenant does not exist");
        }

        // 禁止删除默认租户
        if (1 == tenant.getDefaultFlag()) {
            throw new BusinessException("Deleting default tenants is not allowed");
        }
    }

    @Override
    public List<Tenant> listByCode(Collection<String> codeList) {
        if (ObjectUtil.isEmpty(codeList)) {
            return Lists.arrayList();
        }
        return lambdaQuery()
                .in(Tenant::getTenantCode, codeList)
                .list();
    }

    @Override
    public Tenant getByCode(String code) {
        return lambdaQuery()
                .eq(Tenant::getTenantCode, code)
                .one();
    }

    @Override
    public List<Tenant> list(TenantQueryDTO tenantQueryDTO) {
        return lambdaQuery()
                .in(ObjectUtil.isNotEmpty(tenantQueryDTO.getRefNumList()), Tenant::getTenantCode, tenantQueryDTO.getRefNumList())
                .in(ObjectUtil.isNotEmpty(tenantQueryDTO.getAbbrNameList()), Tenant::getAbbrName, tenantQueryDTO.getAbbrNameList())
                .list();
    }

    /**
     * 初始化租户信息
     * <p>
     * 此方法主要用于在创建新租户时，对租户实体进行必要初始化设置，包括检查租户实体的有效性，
     * 设置租户的状态和租户代码等信息
     *
     * @param entity 租户实体，包含租户的相关信息
     * @return 返回初始化后的租户实体
     * @throws BusinessException 如果传入的租户实体为空，则抛出业务异常
     */
    private Tenant initTenant(Tenant entity) {
        // 检查租户实体是否为空，如果为空则抛出业务异常
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("Tenant cannot be empty");
        }

        // 设置租户状态，如果状态为空，则默认设置为1
        entity.setState(ObjectUtil.nullToDefault(entity.getState(), 1));

        // 返回初始化后的租户实体
        return entity;
    }

    /**
     * 刷新租户缓存
     *
     * @param info 租户基础信息
     */
    private void updateRedis(Tenant info) {
        cacheRepertory.addTenant(BeanUtil.copyNew(info, TenantCache.class));
    }

    /**
     * 根据Tenant实体构建TenantVO对象
     * 此方法旨在将数据库中的Tenant实体转换为视图对象(TenantVO)，以便于在系统中传递或展示
     *
     * @param entity Tenant实体，代表数据库中的租户信息
     * @return 返回构建好的TenantVO对象，如果输入实体为空，则返回null
     */
    private TenantVO buildTenantVO(Tenant entity) {
        // 检查输入的Tenant实体是否为空，如果为空则直接返回null，不再执行后续操作
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }

        // 返回构建好的TenantVO对象
        return Converters.get(TenantConverter.class).toVO(entity);
    }


}
