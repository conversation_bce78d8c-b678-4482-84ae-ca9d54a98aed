package cn.need.cloud.upms.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * PartnerShipCreateParam
 *
 * <AUTHOR>
 * @since 2024-11-22
 */
@Data
@Schema(description = "企业运输新建参数")
public class PartnerShipCreateParam implements Serializable {

    @Serial
    private static final long serialVersionUID = 2382678003490595306L;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 最后更新人
     */
    private Long updateBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人姓名
     */
    private String createByName;

    /**
     * 最后更新人姓名
     */
    private String updateByName;


    /**
     * 提供者合作伙伴ID
     */
    @Schema(description = "提供者合作伙伴ID")
    private Long providerPartnerId;

    /**
     * 客户合作伙伴ID
     */
    @Schema(description = "客户合作伙伴ID")
    private Long clientPartnerId;

    /**
     * 提供者类型
     */
    @Schema(description = "提供者类型")
    private String providerType;

    /**
     * 是否激活
     */
    @Schema(description = "是否激活")
    private Boolean activeFlag;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 版本
     */
    @Schema(description = "版本")
    private Long version;
}