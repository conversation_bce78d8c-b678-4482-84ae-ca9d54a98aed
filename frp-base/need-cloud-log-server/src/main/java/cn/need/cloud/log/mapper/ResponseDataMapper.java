package cn.need.cloud.log.mapper;

import cn.need.cloud.log.model.entity.ResponseData;
import cn.need.framework.common.mybatis.base.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 响应明细数据 Mapper接口
 *
 * <AUTHOR>
 */
public interface ResponseDataMapper extends SuperMapper<ResponseData> {

    /**
     * 根据id集合物理删除数据
     *
     * @param ids 数据id集合
     */
    void deleteByIds(@Param("ids") List<Long> ids);

    /**
     * 清数据
     */
    void truncateTable();

}
