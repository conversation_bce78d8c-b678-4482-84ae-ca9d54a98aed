package cn.need.cloud.dict.service.impl;

import cn.need.cloud.dict.client.dto.RegionDTO;
import cn.need.cloud.dict.client.dto.req.RegionReqDTO;
import cn.need.cloud.dict.mapper.RegionMapper;
import cn.need.cloud.dict.model.entity.Region;
import cn.need.cloud.dict.model.vo.RegionVO;
import cn.need.cloud.dict.service.RegionService;
import cn.need.cloud.dict.util.GeneralUtil;
import cn.need.framework.common.annotation.enums.AreaDepth;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.exception.unchecked.ParameterException;
import cn.need.framework.common.core.lang.EnumUtil;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.core.map.Maps;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.core.text.PinyinUtil;
import cn.need.framework.common.core.text.pattern.PinyinPattern;
import cn.need.framework.common.dict.api.AreaRepertory;
import cn.need.framework.common.dict.entity.Area;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.need.framework.common.core.lang.ObjectUtil.*;

/**
 * 行政区域 服务实现
 *
 * <AUTHOR>
 */
@Service
public class RegionServiceImpl extends SuperServiceImpl<RegionMapper, Region> implements RegionService {

    private final AreaRepertory repertory;

    public RegionServiceImpl(AreaRepertory repertory) {
        this.repertory = repertory;
    }

    //********************************************* 重写父类方法 *********************************************/

    /**
     * 重写新增数据的方法，会将数据写入到redis缓存中
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int insert(Region entity) {
        if (ObjectUtil.isEmpty(entity.getParentId())) {
            setRootInfo(entity);
        } else {
            //设置数据字典的基本信息
            setBasicInfo(entity, null);
        }
        //新增数据，得到数据的id
        int result = super.insert(entity);
        //将id追加至path路径中
        appendIdToPath(entity.getId(), entity.getPath());
        //更新数据至redis缓存
        updateToRedis(entity.getId());
        return result;
    }

    /**
     * 重写修改数据的方法，会将数据写入到redis缓存中
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(Region entity) {
        return updateRegion(entity, super::update);
    }

    /**
     * 重写修改数据全量字段的方法，会将数据写入到redis缓存中
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateForAll(Region entity) {
        return updateRegion(entity, super::updateForAll);
    }

    /**
     * 重写数据删除的方法，会将数据从redis缓存中删除
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int removeById(Long id) {
        //1. 根据字典id获取其映射的字典对象，并根据字典的路径获取其映射的根级字典编号
        Region entity = getById(id);
        Validate.notNull(entity, "不能根据数据字典id[" + id + "]获取到对应的数据！");
        //2. 获取id获取对应的字典及其所有子集字典，并提取数据的id，执行批量删除
        List<Long> ids = listAllByParentId(id, true).stream().map(Region::getId).collect(Collectors.toList());
        int result = super.removeByIds(ids);
        //3. 删除字典编号映射的缓存数据
        repertory.del(entity.getRegionCode(), EnumUtil.getEnum(AreaDepth.class, "depth", entity.getDepth()));
        return result;
    }

    /**
     * 保存根结点信息
     *
     * @param entity 根节点基础信息
     */
    private void setRootInfo(Region entity) {
        entity.setParentRegionCode("0");
        entity.setFullRegionCode(entity.getRegionCode().concat(StringPool.COMMA));
        entity.setFullRegionName(entity.getRegionName().concat(StringPool.COMMA));
        entity.setDepth(0);
        entity.setParentId(0L);
    }

    //********************************************* 公共方法 *********************************************/

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int saveBatch(Integer depth, List<RegionDTO> dtoList) {
        log.info("------------->> 批量保存新增区域， depth={}, dtoList={}", depth, JsonUtil.toJson(dtoList));
        //1. 优先收集行政区域编码、父级行政区域编码
        List<String> regionCodes = dtoList.stream().flatMap(it -> Lists.arrayList(it.getRegionCode(), it.getParentCode()).stream())
                //过滤空数据或是编码为0的数据
                .filter(it -> !GeneralUtil.isParentEmpty(it))
                //去重
                .distinct().collect(Collectors.toList());
        //2. 根据编码集合获取数据，并封装成map，key为行政区域编码，value为行政区域数据
        Map<String, Region> regionMap = ObjectUtil.toMap(listByRegionCodes(regionCodes), Region::getRegionCode);
        //3. 循环构建行政区域数据，若数据存在，添加到新增修改中，否则，添加到新增列表中
        List<Region> inserts = Lists.arrayList();
        List<Region> updates = Lists.arrayList();
        dtoList.forEach(dto -> {
            //优先根据父级编码，获取父级信息
            Region parent = GeneralUtil.isParentEmpty(dto) ? null : Validate.notNull(regionMap.get(dto.getParentCode()),
                    "不能根据父级编码[" + dto.getParentCode() + "]，获取到对应的行政区域数据");
            //再根据行政区域编码，获取已存在的数据
            Region data = regionMap.get(dto.getRegionCode());
            //构建行政区域数据
            Region region = GeneralUtil.buildRegion(dto, parent, data);
            //如果历史数据不为空，添加到修改列表中
            if (isNotNull(data)) {
                updates.add(region);
            }
            //否则，添加到新增列表中
            else {
                inserts.add(region);
            }
        });
        //4. 如果修改列表不为空，执行批量修改
        int result = 0;
        if (!updates.isEmpty()) {
            result += super.updateBatch(updates);
        }
        //5. 如果新增列表不为空，执行批量新增
        if (!inserts.isEmpty()) {
            result += super.insertBatch(inserts);
            //需要再次处理路径信息
            handleRegionPath(inserts);
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int deleteById(Long id) {
        //1. 根据id获取行政区域对象
        Region entity = getById(id);
        if (isNull(entity)) {
            return 0;
        }
        //2. 获取id获取对应的行政区域及其所有子集行政区域，并提取数据的id，执行批量删除
        List<Long> ids = listAllByParentId(id, true).stream().map(Region::getId).collect(Collectors.toList());
        int result = mapper.deleteByIds(ids);
        //3. 删除行政区域编号映射的缓存数据
        repertory.del(entity.getRegionCode(), EnumUtil.getEnum(AreaDepth.class, "depth", entity.getDepth()));
        return result;
    }

    @Override
    public int initialize() {
        //获取所有
        List<Region> dataList = this.query().orderByAsc("depth", "region_code").list();
        //字典封装成map对象，根据id
        Map<Long, Region> dataMap = buildDataMap(dataList);
        log.info("------------ 数据库获取全部行政区域完成，总数：{}{}", dataMap.size(), " ------------");
        //构建Area列表集合
        List<Area> areaList = Lists.arrayList();
        for (Region data : dataMap.values()) {
            Area area = GeneralUtil.buildArea(data, dataMap.get(data.getParentId()));
            //构建全名称、全编码
            //buildFullInfo(area, dataMap);
            areaList.add(area);
        }
        log.info("------------ 数据Area对象完成，总数：{}{}", areaList.size(), " ------------");
        repertory.initialization(areaList);
        log.info("============ 初始化行政区域至redis完成 ============");
        return areaList.size();
    }

    @Override
    public Region getByRegionCode(String regionCode) {
        return lambdaQuery().eq(Region::getRegionCode, regionCode).one();
    }

    @Override
    public List<Region> listByRegionCodes(List<String> regionCodes) {
        return isEmpty(regionCodes) ? Lists.arrayList() : lambdaQuery().in(Region::getRegionCode, regionCodes).list();
    }

    @Override
    public List<Region> listByParentId(Long parentId) {
        return lambdaQuery().eq(Region::getParentId, nullToDefault(parentId, 0L)).list();
    }

    @Override
    public List<Region> listAllByParentId(Long parentId, boolean containSelf) {
        Validate.isTrue(ObjectUtil.gtZero(parentId), "数据行政区域的父级id[{}]不合规", parentId);
        //获取父级id对应的行政区域数据
        Region entity = getById(parentId);
        Validate.notNull(entity, "不能根据行政区域id[" + parentId + "]获取到对应的数据！");
        //根据行政区域路径获取所有数据，并过滤当前父级id映射的行政区域数据
        List<Region> list = listLikePath(entity.getPath());
        //不需要包含自己
        if (!containSelf) {
            list.removeIf(it -> equal(it.getId(), entity.getId()));
        }
        return list;
    }

    @Override
    public List<Region> listLikePath(String path) {
        return isEmpty(path) ? Lists.arrayList() : lambdaQuery().likeRight(Region::getPath, path).list();
    }

    @Override
    public List<Region> listValueSet(String path) {
        //根据"."切割路径，得到数据行政区域的id集合，取集合的第一个id作为根级id
        List<Long> ids = StringUtil.splitToLong(path, StringPool.COMMA);
        return ids.isEmpty() ? Lists.arrayList() : listAllByParentId(ids.get(0), true);
    }

    @Override
    public List<Region> findByCondition(RegionReqDTO condition) {
        LambdaQueryChainWrapper<Region> query = lambdaQuery();
        if (isNotEmpty(condition.getDepth())) {
            query.eq(Region::getDepth, condition.getDepth());
        }
        if (isNotEmpty(condition.getRegionNameList())) {
            query.in(Region::getRegionName, condition.getRegionNameList());
        }
        if (isNotEmpty(condition.getParentIds())) {
            query.in(Region::getParentId, condition.getParentIds());
        }
        return query.list();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchInsert(List<Region> regionList) {
        List<List<Region>> listList = ListUtils.partition(regionList, 200);
        listList.forEach(list -> {
            insertOrUpdateBatch(list);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchUpdate(List<Region> regionList) {
        List<List<Region>> listList = ListUtils.partition(regionList, 200);
        listList.forEach(list -> {
            updateBatch(list);
        });
    }

    @Override
    public List<Region> listAll() {
        return this.mapper.listAll();
    }

    @Override
    public RegionVO detailById(Long id) {
        Region entity = getById(id);
        if (isNull(entity)) {
            throw new BusinessException("区域信息不存在");
        }
        RegionVO vo = BeanUtil.copyNew(entity, RegionVO.class);
        if (isNotNull(vo.getParentId()) || vo.getParentId() > 0L) {
            Region parent = getById(vo.getParentId());
            if (isNotNull(parent)) {
                vo.setParentName(parent.getRegionName());
                vo.setParentRegionCode(parent.getRegionCode());
            }
        }
        return vo;
    }

    /**
     * 批量处理行政区域路径
     */
    private void handleRegionPath(List<Region> inserts) {
        List<Region> updates = inserts.stream().map(it -> {
            Region update = new Region();
            update.setId(it.getId());
            update.setPath(it.getPath() + it.getId() + StringPool.COMMA);
            return update;
        }).collect(Collectors.toList());
        //执行批量保存
        super.updateBatch(updates);
    }

    //********************************************* 私有方法 *********************************************/

    /**
     * 新增或是修改时，设置基本信息
     *
     * @param entity 需要设置的数据行政区域对象
     * @param data   数据库已存在的数据行政区域对象，id与设置数据对象的id一直，新增时，该参数一定会是null
     */
    private void setBasicInfo(Region entity, Region data) {
        //1. 设置与父级相关联的信息，包含了父级id、深度、路径
        setParentInfo(entity, data);
        //2. 设置排序值
        setSorting(entity, data);
        //3. 设置其他信息
        setOther(entity, data);
    }

    private void setOther(Region entity, Region data) {
        //设置拼音
        entity.setRegionNamePy(PinyinUtil.toPinyin(entity.getRegionName(), PinyinPattern.NONE_TONE_FU));
        //设置简拼
        entity.setShortNamePy(PinyinUtil.toShortPinyin(entity.getRegionName()).toUpperCase(Locale.ROOT));
        //设置简称
        if (isEmpty(entity.getShortName())) {
            if (isNotNull(data) && isNotEmpty(data.getShortName())) {
                entity.setShortName(data.getShortName());
            }
        }
    }

    /**
     * 设置行政区域的父级信息，包括当前行政区域父级id、当前行政区域的深度、当前行政区域的路径
     *
     * @param entity 当前需要设置值的数据行政区域对象
     * @param data   已存在的数据行政区域对象，如果为null，表示在新增数据
     */
    private void setParentInfo(Region entity, Region data) {
        //1. 情况一，如果父级id>0
        if (gtZero(entity.getParentId())) {
            //获取父级id，并设置深度、路径
            Region parent = getById(entity.getParentId());
            Validate.notNull(parent, "不能根据行政区域的父级id[" + entity.getParentId() + "]获取到获取到对应的数据！");
            entity.setDepth(parent.getDepth() + 1);
            entity.setPath(isNull(data) ? parent.getPath() : parent.getPath().concat(data.getId().toString()).concat(StringPool.COMMA));
            appendParentData(entity, parent);
            return;
        }
        //2. 情况二，父级id为null，并且已经存在了历史数据
        if (isNull(entity.getParentId()) && isNotNull(data)) {
            entity.setParentId(data.getParentId());
            entity.setDepth(data.getDepth());
            entity.setPath(data.getPath());
            return;
        }
        //3. 其他情况下，默认设置
        entity.setParentId(0L);
        entity.setDepth(0);
        entity.setPath(isNull(data) ? StringPool.EMPTY : data.getId().toString().concat(StringPool.COMMA));
    }

    private void appendParentData(Region entity, Region parent) {
        if (isNull(parent)) {
            return;
        }
        if (isNull(parent.getFullRegionName())) {
            return;
        }
        entity.setFullRegionName(parent.getFullRegionName().concat(entity.getRegionName()).concat(StringPool.COMMA));
        if (isNull(parent.getFullRegionCode())) {
            return;
        }
        entity.setFullRegionCode(parent.getFullRegionCode().concat(entity.getRegionCode().concat(StringPool.COMMA)));
    }

    /**
     * 设置行政区域排序值
     */
    private void setSorting(Region entity, Region data) {
        //1. 情况一，当前设值对象的排序值不为空，直接退出
        if (isNotNull(entity.getSorting())) {
            return;
        }
        //2. 情况二，当前设值对象的排序值为空，并且已存在的行政区域的排序值不为空
        if (isNotNull(data) && isNotNull(data.getSorting())) {
            entity.setSorting(data.getSorting());
            return;
        }
        //3. 其他情况，设置为默认排序值
        entity.setSorting(0D);
    }

    /**
     * 将行政区域的id，追加至行政区域的path路径中，并保存到数据库
     *
     * @param id   行政区域的id
     * @param path 行政区域路径
     */
    private void appendIdToPath(Long id, String path) {
        Region entity = new Region();
        entity.setId(id);
        entity.setPath(nullToDefault(path, StringPool.EMPTY).concat(id.toString()).concat(StringPool.COMMA));
        Validate.isTrue(super.update(entity) > 0, "更新数据行政区域路径失败！");
    }

    /**
     * 修改数据行政区域，接口函数用来执行实际的入库操作方法
     *
     * @param entity   需要修改的行政区域数据
     * @param function 接口函数
     * @return int 受影响行数
     */
    private int updateRegion(Region entity, Function<Region, Integer> function) {
        //优先根据id从数据库中获取已存在的行政区域数据，并根据行政区域的路径获取其映射的根级行政区域编号
        Region data = getById(entity.getId());
        Validate.notNull(data, "不能根据行政区域id[" + entity.getId() + "]获取到对应的数据！");
        //不允许修改编码
        if (!equal(entity.getRegionCode(), data.getRegionCode())) {
            throw new ParameterException("行政区域修改，不允许修改编码！");
        }
        //设置行政区域的基本信息
        setBasicInfo(entity, data);
        //更新数据
        int result = function.apply(entity);
        //更新数据至缓存
        updateToRedis(entity.getId());
        return result;
    }

    /**
     * 更新行政区域数据至redis缓存中
     *
     * @param id 行政区域id
     */
    private void updateToRedis(Long id) {
        //1. 根据id获取行政区域对象、行政区域的父级对象、根级行政区域对象
        Region entity = getById(id);
        Region parent = getById(entity.getParentId());
        //2. 将数据添加至redis缓存
        repertory.add(GeneralUtil.buildArea(entity, parent));
    }

    /**
     * 构建全名称、全编码信息
     *
     * @param area    字典行政区域数据对象
     * @param dataMap 全部行政区域数据map集合
     */
    private void buildFullInfo(Area area, Map<Long, Region> dataMap) {
        if (StringUtil.isEmpty(area.getPath())) {
            return;
        }
        String[] temp = area.getPath().split(StringPool.COMMA);
        StringBuilder codes = new StringBuilder();
        StringBuilder names = new StringBuilder();
        for (String id : temp) {
            if (StringUtil.isEmpty(id)) {
                continue;
            }
            Region region = dataMap.get(Long.valueOf(id));
            if (ObjectUtil.isNotNull(region)) {
                codes.append(region.getRegionCode()).append(StringPool.COMMA);
                names.append(region.getRegionName()).append(StringPool.COMMA);
            }
        }
        area.setFullCode(codes.substring(0, codes.length() - 1));
        area.setFullName(names.substring(0, names.length() - 1));
    }

    /**
     * 构建行政区域数据的map集合
     */
    private Map<Long, Region> buildDataMap(List<Region> dataList) {
        Map<Long, Region> map = Maps.hashMap();
        for (Region data : dataList) {
            map.put(data.getId(), data);
        }
        return map;
    }
}
