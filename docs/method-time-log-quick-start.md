# 方法耗时统计功能 - 快速开始指南

## 🚀 快速开始

### 1. 启用功能（1分钟配置）

在 `application.yml` 中添加以下配置：

```yaml
# 启用方法耗时统计
method:
  time:
    log:
      enable: true

# 设置日志级别为TRACE
logging:
  level:
    cn.need.framework.starter.web.aspect.MethodTimeLogAspect: TRACE
    # 可选：为特定业务包启用TRACE级别
    cn.need.cloud.biz.service: TRACE
```

### 2. 重启应用

重启应用后，功能自动生效。

### 3. 查看日志输出

调用任何业务接口，查看日志输出：

```
TRACE [METHOD-TIME] Method [ProductService.createProduct()] started [TraceId: abc123-def456]
TRACE [METHOD-TIME] Method [ProductService.createProduct()] completed successfully, duration: 45ms [TraceId: abc123-def456]
```

## 🎯 核心特性

| 特性 | 说明 | 示例 |
|------|------|------|
| **自动拦截** | 无需修改代码，自动统计所有public方法 | 所有Service、Controller方法 |
| **Trace级别** | 只在TRACE级别启用，不影响生产性能 | 开发环境启用，生产环境关闭 |
| **慢方法告警** | 超过阈值的方法以WARN级别记录 | 默认1000ms，可自定义 |
| **TraceId集成** | 自动显示链路追踪ID | 便于问题排查 |
| **注解控制** | 使用@MethodTimeLog精确控制 | 记录参数、返回值等 |

## 📝 使用示例

### 基础使用（无需修改代码）

```java
@Service
public class ProductService {
    
    // 这个方法会自动记录耗时，无需任何修改
    public Product createProduct(ProductCreateParam param) {
        // 业务逻辑
        return product;
    }
}
```

### 高级使用（注解控制）

```java
@Service
public class ProductService {
    
    // 记录参数和返回值，设置慢方法阈值为500ms
    @MethodTimeLog(logArgs = true, logResult = true, slowThreshold = 500L)
    public Product updateProduct(ProductUpdateParam param) {
        // 业务逻辑
        return product;
    }
}
```

## 🔧 环境配置

### 开发环境（推荐配置）

```yaml
method.time.log.enable: true
logging:
  level:
    cn.need.framework.starter.web.aspect.MethodTimeLogAspect: TRACE
    cn.need.cloud.biz: TRACE  # 查看所有业务方法
```

### 测试环境

```yaml
method.time.log.enable: true
logging:
  level:
    cn.need.framework.starter.web.aspect.MethodTimeLogAspect: TRACE
    cn.need.cloud.biz: INFO   # 只看慢方法告警
```

### 生产环境

```yaml
method.time.log.enable: false  # 默认关闭
logging:
  level:
    cn.need.framework.starter.web.aspect.MethodTimeLogAspect: INFO
```

### 生产环境临时调试

```yaml
method.time.log.enable: true   # 临时启用
logging:
  level:
    cn.need.framework.starter.web.aspect.MethodTimeLogAspect: TRACE
    # 只为特定包启用
    cn.need.cloud.biz.service.product: TRACE
```

## 📊 日志输出格式

### 正常方法执行

```
TRACE [METHOD-TIME] Method [ProductService.createProduct()] started [TraceId: abc123-def456]
TRACE [METHOD-TIME] Method [ProductService.createProduct()] completed successfully, duration: 45ms [TraceId: abc123-def456]
```

### 慢方法告警

```
WARN [METHOD-TIME] [SLOW METHOD] Method [ProductService.complexQuery()] completed successfully, duration: 1500ms [TraceId: abc123-def456]
```

### 带参数和返回值

```
TRACE [METHOD-TIME] Method [ProductService.updateProduct()] started with args: [ProductUpdateParam{id=123}] [TraceId: abc123-def456]
TRACE [METHOD-TIME] Method [ProductService.updateProduct()] completed successfully with result: Product{id=123}, duration: 120ms [TraceId: abc123-def456]
```

### 异常情况

```
TRACE [METHOD-TIME] Method [ProductService.deleteProduct()] failed with exception: BusinessException - Product not found, duration: 15ms [TraceId: abc123-def456]
```

## 🎛️ 注解参数说明

```java
@MethodTimeLog(
    logArgs = true,           // 是否记录方法参数
    logResult = true,         // 是否记录返回值
    slowThreshold = 500L,     // 慢方法阈值（毫秒）
    description = "创建产品"   // 自定义描述
)
```

## 🚫 排除的方法

以下方法不会被拦截，不会记录耗时：

- getter方法：`get*()`
- setter方法：`set*()`
- 布尔判断：`is*()`, `has*()`
- Object方法：`toString()`, `hashCode()`, `equals()`

## ⚡ 性能影响

- **开发环境**：可忽略的性能影响（< 1ms per method）
- **生产环境**：关闭时零性能影响
- **内存占用**：每个方法调用增加一个long类型时间戳

## 🔍 故障排查

### 问题：看不到日志输出

**解决方案：**
1. 检查配置：`method.time.log.enable=true`
2. 检查日志级别：`logging.level.cn.need.framework.starter.web.aspect.MethodTimeLogAspect=TRACE`
3. 确认方法符合拦截规则（public方法，不是getter/setter）

### 问题：方法没有被拦截

**解决方案：**
1. 确认方法在 `cn.need.cloud` 包下
2. 确认方法是public的
3. 确认方法不在排除列表中

### 问题：性能影响

**解决方案：**
1. 关闭功能：`method.time.log.enable=false`
2. 调整日志级别为INFO或更高
3. 使用注解精确控制需要统计的方法

## 📚 更多文档

- [详细使用指南](method-time-log-guide.md)
- [配置示例](method-time-log-example-config.yml)
- [使用示例代码](method-time-log-usage-example.java)
- [实现总结](method-time-log-implementation-summary.md)

## 💡 最佳实践

1. **开发环境**：启用所有功能，便于调试
2. **生产环境**：默认关闭，需要时临时启用
3. **敏感数据**：不要记录包含敏感信息的参数
4. **大对象**：避免记录大对象的返回值
5. **慢方法阈值**：根据业务特点设置合理的阈值

---

🎉 **恭喜！** 您已经成功配置了方法耗时统计功能。现在可以轻松监控和排查性能问题了！
