# 方法耗时统计功能使用指南

## 概述

本功能通过AOP切面为所有public方法提供trace级别的耗时统计，方便排查慢方法和性能问题。

## 功能特性

1. **自动拦截所有public方法** - 无需手动添加代码
2. **Trace级别日志输出** - 不影响生产环境性能
3. **慢方法告警** - 超过阈值的方法会以WARN级别记录
4. **TraceId集成** - 支持链路追踪
5. **可选参数记录** - 支持记录方法参数和返回值
6. **异常处理** - 记录方法执行异常信息
7. **性能优化** - 只在TRACE级别启用时才进行统计

## 配置说明

### 1. 启用方法耗时统计

在 `application.yml` 中添加配置：

```yaml
# 启用方法耗时统计
method:
  time:
    log:
      enable: true

# 设置日志级别为TRACE（开发环境）
logging:
  level:
    cn.need.cloud: TRACE
    cn.need.framework.starter.web.aspect.MethodTimeLogAspect: TRACE
```

### 2. 生产环境配置

生产环境建议只在需要时临时启用：

```yaml
# 生产环境配置
method:
  time:
    log:
      enable: false  # 默认关闭

# 生产环境日志级别
logging:
  level:
    cn.need.cloud: INFO
```

## 使用方式

### 1. 自动拦截

所有符合条件的public方法会被自动拦截：

```java
@Service
public class ProductService {
    
    // 这个方法会被自动拦截并记录耗时
    public Product createProduct(ProductCreateParam param) {
        // 业务逻辑
        return product;
    }
    
    // getter/setter方法会被排除，不会记录耗时
    public String getName() {
        return name;
    }
}
```

### 2. 使用注解进行精确控制

使用 `@MethodTimeLog` 注解可以对特定方法进行更精确的控制：

```java
@Service
public class ProductService {
    
    // 记录参数和返回值，设置慢方法阈值为500ms
    @MethodTimeLog(logArgs = true, logResult = true, slowThreshold = 500L)
    public Product updateProduct(ProductUpdateParam param) {
        // 业务逻辑
        return product;
    }
    
    // 只记录耗时，不记录参数和返回值
    @MethodTimeLog
    public List<Product> queryProducts(ProductQuery query) {
        // 业务逻辑
        return products;
    }
}
```

### 3. 类级别注解

可以在类上使用注解，对整个类的所有public方法生效：

```java
@Service
@MethodTimeLog(slowThreshold = 2000L)
public class ProductService {
    
    // 继承类级别的配置
    public Product createProduct(ProductCreateParam param) {
        return product;
    }
    
    // 方法级别的注解会覆盖类级别的配置
    @MethodTimeLog(logArgs = true, slowThreshold = 1000L)
    public Product updateProduct(ProductUpdateParam param) {
        return product;
    }
}
```

## 注解参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `logArgs` | boolean | false | 是否记录方法参数 |
| `logResult` | boolean | false | 是否记录返回值 |
| `slowThreshold` | long | 1000L | 慢方法阈值（毫秒） |
| `description` | String | "" | 自定义方法描述 |

## 日志输出示例

### 正常方法执行

```
TRACE [METHOD-TIME] Method [ProductService.createProduct()] started [TraceId: abc123-def456]
TRACE [METHOD-TIME] Method [ProductService.createProduct()] completed successfully, duration: 45ms [TraceId: abc123-def456]
```

### 慢方法告警

```
WARN [METHOD-TIME] [SLOW METHOD] Method [ProductService.queryProducts()] completed successfully, duration: 1500ms [TraceId: abc123-def456]
```

### 带参数和返回值的记录

```
TRACE [METHOD-TIME] Method [ProductService.updateProduct()] started with args: [ProductUpdateParam{id=123, name='test'}] [TraceId: abc123-def456]
TRACE [METHOD-TIME] Method [ProductService.updateProduct()] completed successfully with result: Product{id=123, name='test'}, duration: 120ms [TraceId: abc123-def456]
```

### 异常情况

```
TRACE [METHOD-TIME] Method [ProductService.deleteProduct()] started [TraceId: abc123-def456]
TRACE [METHOD-TIME] Method [ProductService.deleteProduct()] failed with exception: BusinessException - Product not found, duration: 15ms [TraceId: abc123-def456]
```

## 拦截规则

### 包含的方法
- `cn.need.cloud` 包下的所有public方法
- 带有 `@MethodTimeLog` 注解的方法

### 排除的方法
- getter方法（`get*`）
- setter方法（`set*`）
- 布尔判断方法（`is*`, `has*`）
- Object基础方法（`toString`, `hashCode`, `equals`）

## 性能考虑

1. **只在TRACE级别启用** - 生产环境通常不会开启TRACE级别
2. **最小化性能影响** - 只记录时间戳，不进行复杂计算
3. **可配置开关** - 通过配置文件控制是否启用
4. **智能排除** - 自动排除不需要统计的方法

## 故障排查

### 1. 方法没有被拦截

检查以下配置：
- 确认 `method.time.log.enable=true`
- 确认日志级别为TRACE
- 确认方法符合拦截规则

### 2. 看不到日志输出

检查日志配置：
```yaml
logging:
  level:
    cn.need.framework.starter.web.aspect.MethodTimeLogAspect: TRACE
```

### 3. 性能影响

如果担心性能影响，可以：
- 关闭功能：`method.time.log.enable=false`
- 调整日志级别为INFO或更高
- 使用注解精确控制需要统计的方法

## 最佳实践

1. **开发环境** - 启用所有功能，便于调试
2. **测试环境** - 启用耗时统计，用于性能测试
3. **生产环境** - 默认关闭，需要时临时启用
4. **敏感数据** - 不要在包含敏感信息的方法上启用参数记录
5. **大对象** - 避免记录大对象的返回值，影响性能
