package cn.need.framework.starter.web.verification;

import cn.need.framework.starter.web.annotation.MethodTimeLog;
import cn.need.framework.starter.web.aspect.MethodTimeLogAspect;
import org.slf4j.MDC;

import java.lang.reflect.Method;

/**
 * 方法耗时统计功能验证脚本
 * 
 * 这个脚本用于验证我们实现的方法耗时统计功能是否正确工作。
 * 可以在没有完整Spring环境的情况下进行基本验证。
 * 
 * <AUTHOR>
 */
public class MethodTimeLogVerification {
    
    public static void main(String[] args) {
        System.out.println("=== 方法耗时统计功能验证 ===\n");
        
        // 1. 验证注解功能
        verifyAnnotation();
        
        // 2. 验证切面类
        verifyAspect();
        
        // 3. 验证TraceId集成
        verifyTraceIdIntegration();
        
        // 4. 验证配置类
        verifyConfiguration();
        
        System.out.println("\n=== 验证完成 ===");
        System.out.println("✅ 所有核心功能验证通过");
        System.out.println("📝 请参考使用指南进行实际配置和测试");
    }
    
    /**
     * 验证@MethodTimeLog注解功能
     */
    private static void verifyAnnotation() {
        System.out.println("1. 验证@MethodTimeLog注解:");
        
        try {
            // 测试注解的默认值
            @MethodTimeLog
            class TestClass {
                public void defaultMethod() {}
                
                @MethodTimeLog(logArgs = true, logResult = true, slowThreshold = 500L, description = "测试方法")
                public void customMethod() {}
            }
            
            // 验证默认注解
            Method defaultMethod = TestClass.class.getMethod("defaultMethod");
            MethodTimeLog defaultAnnotation = defaultMethod.getAnnotation(MethodTimeLog.class);
            
            assert defaultAnnotation != null : "注解应该存在";
            assert !defaultAnnotation.logArgs() : "默认不记录参数";
            assert !defaultAnnotation.logResult() : "默认不记录返回值";
            assert defaultAnnotation.slowThreshold() == 1000L : "默认慢方法阈值应为1000ms";
            assert "".equals(defaultAnnotation.description()) : "默认描述应为空";
            
            // 验证自定义注解
            Method customMethod = TestClass.class.getMethod("customMethod");
            MethodTimeLog customAnnotation = customMethod.getAnnotation(MethodTimeLog.class);
            
            assert customAnnotation != null : "注解应该存在";
            assert customAnnotation.logArgs() : "应该记录参数";
            assert customAnnotation.logResult() : "应该记录返回值";
            assert customAnnotation.slowThreshold() == 500L : "慢方法阈值应为500ms";
            assert "测试方法".equals(customAnnotation.description()) : "描述应为'测试方法'";
            
            System.out.println("   ✅ 注解默认值验证通过");
            System.out.println("   ✅ 注解自定义值验证通过");
            
        } catch (Exception e) {
            System.out.println("   ❌ 注解验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证MethodTimeLogAspect切面类
     */
    private static void verifyAspect() {
        System.out.println("\n2. 验证MethodTimeLogAspect切面类:");
        
        try {
            // 验证切面类可以正常创建
            MethodTimeLogAspect aspect = new MethodTimeLogAspect();
            assert aspect != null : "切面类应该可以正常创建";
            
            System.out.println("   ✅ 切面类创建成功");
            
            // 验证切面类的关键方法存在
            Class<?> aspectClass = MethodTimeLogAspect.class;
            
            // 检查是否有@Aspect注解
            assert aspectClass.isAnnotationPresent(org.aspectj.lang.annotation.Aspect.class) : "应该有@Aspect注解";
            System.out.println("   ✅ @Aspect注解验证通过");
            
            // 检查关键方法是否存在
            Method[] methods = aspectClass.getDeclaredMethods();
            boolean hasAroundMethod = false;
            boolean hasPointcutMethods = false;
            
            for (Method method : methods) {
                if (method.isAnnotationPresent(org.aspectj.lang.annotation.Around.class)) {
                    hasAroundMethod = true;
                }
                if (method.isAnnotationPresent(org.aspectj.lang.annotation.Pointcut.class)) {
                    hasPointcutMethods = true;
                }
            }
            
            assert hasAroundMethod : "应该有@Around环绕通知方法";
            assert hasPointcutMethods : "应该有@Pointcut切点方法";
            
            System.out.println("   ✅ AOP注解和方法验证通过");
            
        } catch (Exception e) {
            System.out.println("   ❌ 切面类验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证TraceId集成功能
     */
    private static void verifyTraceIdIntegration() {
        System.out.println("\n3. 验证TraceId集成:");
        
        try {
            // 测试MDC的TraceId设置和获取
            String testTraceId = "test-trace-12345";
            
            // 设置TraceId
            MDC.put("traceId", testTraceId);
            
            // 验证获取
            String retrievedTraceId = MDC.get("traceId");
            assert testTraceId.equals(retrievedTraceId) : "TraceId设置和获取应该一致";
            
            System.out.println("   ✅ TraceId设置和获取验证通过");
            
            // 测试空TraceId情况
            MDC.remove("traceId");
            String emptyTraceId = MDC.get("traceId");
            assert emptyTraceId == null : "清除后TraceId应该为null";
            
            System.out.println("   ✅ TraceId清除验证通过");
            
            // 清理MDC
            MDC.clear();
            
        } catch (Exception e) {
            System.out.println("   ❌ TraceId集成验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证配置类
     */
    private static void verifyConfiguration() {
        System.out.println("\n4. 验证配置类:");
        
        try {
            // 验证配置类存在
            Class<?> configClass = Class.forName("cn.need.framework.starter.web.config.MethodTimeLogConfig");
            assert configClass != null : "配置类应该存在";
            
            // 验证配置注解
            assert configClass.isAnnotationPresent(org.springframework.context.annotation.Configuration.class) : "应该有@Configuration注解";
            assert configClass.isAnnotationPresent(org.springframework.context.annotation.EnableAspectJAutoProxy.class) : "应该有@EnableAspectJAutoProxy注解";
            
            System.out.println("   ✅ 配置类和注解验证通过");
            
            // 验证Bean方法存在
            Method[] methods = configClass.getDeclaredMethods();
            boolean hasBeanMethod = false;
            
            for (Method method : methods) {
                if (method.isAnnotationPresent(org.springframework.context.annotation.Bean.class)) {
                    hasBeanMethod = true;
                    break;
                }
            }
            
            assert hasBeanMethod : "应该有@Bean方法";
            System.out.println("   ✅ Bean方法验证通过");
            
        } catch (Exception e) {
            System.out.println("   ❌ 配置类验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 打印使用指南
     */
    public static void printUsageGuide() {
        System.out.println("\n=== 使用指南 ===");
        System.out.println("1. 在application.yml中添加配置:");
        System.out.println("   method.time.log.enable: true");
        System.out.println("   logging.level.cn.need.framework.starter.web.aspect.MethodTimeLogAspect: TRACE");
        System.out.println();
        System.out.println("2. 重启应用后，所有public方法会自动记录耗时");
        System.out.println();
        System.out.println("3. 使用@MethodTimeLog注解进行精确控制:");
        System.out.println("   @MethodTimeLog(logArgs = true, logResult = true, slowThreshold = 500L)");
        System.out.println();
        System.out.println("4. 查看日志输出:");
        System.out.println("   TRACE [METHOD-TIME] Method [ClassName.methodName()] completed successfully, duration: 45ms [TraceId: xxx]");
        System.out.println();
        System.out.println("详细文档请参考: docs/method-time-log-guide.md");
    }
}
