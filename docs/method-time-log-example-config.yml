# 方法耗时统计功能配置示例

# ================================
# 开发环境配置示例
# ================================
spring:
  profiles:
    active: dev

---
# 开发环境
spring:
  config:
    activate:
      on-profile: dev

# 启用方法耗时统计
method:
  time:
    log:
      enable: true

# 开发环境日志配置
logging:
  level:
    root: INFO
    # 启用方法耗时统计的TRACE日志
    cn.need.framework.starter.web.aspect.MethodTimeLogAspect: TRACE
    # 业务包的TRACE日志（可选，用于查看所有方法调用）
    cn.need.cloud.biz.service: TRACE
    cn.need.cloud.biz.controller: TRACE
    # 其他框架日志保持DEBUG级别
    cn.need.framework.starter.web.log.RequestLoggingFilter: DEBUG
    cn.need.framework.common.mybatis.log.MyBatisParseLoggingImpl: DEBUG

---
# ================================
# 测试环境配置示例
# ================================
spring:
  config:
    activate:
      on-profile: test

# 测试环境启用方法耗时统计，用于性能测试
method:
  time:
    log:
      enable: true

# 测试环境日志配置
logging:
  level:
    root: INFO
    # 只启用方法耗时统计，不启用业务包的详细日志
    cn.need.framework.starter.web.aspect.MethodTimeLogAspect: TRACE
    # 保持请求日志用于接口测试
    cn.need.framework.starter.web.log.RequestLoggingFilter: DEBUG

---
# ================================
# 生产环境配置示例
# ================================
spring:
  config:
    activate:
      on-profile: prod

# 生产环境默认关闭方法耗时统计
method:
  time:
    log:
      enable: false

# 生产环境日志配置
logging:
  level:
    root: WARN
    cn.need.cloud: INFO
    # 生产环境关闭TRACE日志
    cn.need.framework.starter.web.aspect.MethodTimeLogAspect: INFO
    # 保持请求日志用于监控
    cn.need.framework.starter.web.log.RequestLoggingFilter: INFO

---
# ================================
# 生产环境临时调试配置示例
# ================================
spring:
  config:
    activate:
      on-profile: prod-debug

# 生产环境临时启用方法耗时统计（用于排查性能问题）
method:
  time:
    log:
      enable: true

# 临时调试日志配置
logging:
  level:
    root: WARN
    cn.need.cloud: INFO
    # 临时启用方法耗时统计
    cn.need.framework.starter.web.aspect.MethodTimeLogAspect: TRACE
    # 可以针对特定包启用详细日志
    cn.need.cloud.biz.service.product: TRACE
    cn.need.cloud.biz.service.inventory: TRACE

---
# ================================
# 性能测试专用配置示例
# ================================
spring:
  config:
    activate:
      on-profile: performance

# 性能测试环境配置
method:
  time:
    log:
      enable: true

# 性能测试日志配置
logging:
  level:
    root: WARN
    # 只记录慢方法告警
    cn.need.framework.starter.web.aspect.MethodTimeLogAspect: WARN
    # 关闭其他详细日志，减少I/O影响
    cn.need.framework.starter.web.log.RequestLoggingFilter: WARN
    cn.need.framework.common.mybatis.log.MyBatisParseLoggingImpl: WARN

# ================================
# 其他相关配置示例
# ================================

# 请求日志配置（与方法耗时统计配合使用）
servlet:
  request:
    log:
      enable: true
      include-query-string: true
      include-payload: true
      include-headers: false
      include-client-info: false
      truncation-length: -1

# 数据库日志配置
mybatis-plus:
  configuration:
    log-impl: cn.need.framework.common.mybatis.log.MyBatisParseLoggingImpl

# ================================
# 动态日志级别调整示例
# ================================
# 可以通过actuator端点动态调整日志级别，无需重启应用

# 启用actuator
management:
  endpoints:
    web:
      exposure:
        include: loggers
  endpoint:
    loggers:
      enabled: true

# 使用示例：
# POST /actuator/loggers/cn.need.framework.starter.web.aspect.MethodTimeLogAspect
# {
#   "configuredLevel": "TRACE"
# }

# ================================
# 日志文件配置示例（logback-spring.xml中的配置）
# ================================
# 可以将方法耗时日志单独输出到文件：
#
# <appender name="METHOD_TIME_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
#     <file>${log.path}/method-time.log</file>
#     <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
#         <fileNamePattern>${log.path}/method-time.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
#         <maxFileSize>100MB</maxFileSize>
#         <maxHistory>30</maxHistory>
#         <totalSizeCap>3GB</totalSizeCap>
#     </rollingPolicy>
#     <encoder>
#         <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
#     </encoder>
# </appender>
#
# <logger name="cn.need.framework.starter.web.aspect.MethodTimeLogAspect" level="TRACE" additivity="false">
#     <appender-ref ref="METHOD_TIME_FILE"/>
# </logger>
