package cn.need.framework.starter.web.aspect;

import cn.need.framework.starter.web.annotation.MethodTimeLog;
import org.junit.jupiter.api.Test;
import org.slf4j.MDC;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.stereotype.Service;
import org.springframework.test.context.TestPropertySource;

import javax.annotation.Resource;

/**
 * 方法耗时统计集成测试示例
 * 
 * 这个测试类展示了如何在实际项目中测试方法耗时统计功能。
 * 注意：这是一个示例文件，实际使用时需要根据项目结构调整。
 * 
 * <AUTHOR>
 */
@SpringBootTest
@TestPropertySource(properties = {
    "method.time.log.enable=true",
    "logging.level.cn.need.framework.starter.web.aspect.MethodTimeLogAspect=TRACE"
})
public class MethodTimeLogIntegrationTest {
    
    @Resource
    private TestBusinessService testBusinessService;
    
    /**
     * 测试业务服务类
     * 模拟实际的业务服务，用于验证AOP功能
     */
    @Service
    public static class TestBusinessService {
        
        /**
         * 普通业务方法 - 会被自动拦截
         */
        public String processOrder(String orderId) {
            // 模拟业务处理
            try {
                Thread.sleep(50); // 模拟处理时间
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            return "Order " + orderId + " processed";
        }
        
        /**
         * 带注解的方法 - 记录参数和返回值
         */
        @MethodTimeLog(logArgs = true, logResult = true, slowThreshold = 100L)
        public String createProduct(String productName, int quantity) {
            // 模拟产品创建
            try {
                Thread.sleep(30);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            return String.format("Product %s created with quantity %d", productName, quantity);
        }
        
        /**
         * 慢方法 - 会触发慢方法告警
         */
        @MethodTimeLog(slowThreshold = 50L)
        public String slowOperation() {
            try {
                Thread.sleep(100); // 超过阈值
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            return "Slow operation completed";
        }
        
        /**
         * 抛异常的方法 - 测试异常处理
         */
        public String methodWithException() {
            throw new RuntimeException("Business exception for testing");
        }
        
        /**
         * 不会被拦截的getter方法
         */
        public String getName() {
            return "TestService";
        }
        
        /**
         * 不会被拦截的setter方法
         */
        public void setName(String name) {
            // setter logic
        }
    }
    
    /**
     * 测试普通方法的耗时统计
     */
    @Test
    public void testNormalMethodTimeLogging() {
        // 设置TraceId
        MDC.put("traceId", "test-trace-001");
        
        try {
            String result = testBusinessService.processOrder("ORDER-123");
            
            // 验证结果
            assert result.contains("ORDER-123");
            assert result.contains("processed");
            
            // 日志输出示例：
            // TRACE [METHOD-TIME] Method [TestBusinessService.processOrder()] started [TraceId: test-trace-001]
            // TRACE [METHOD-TIME] Method [TestBusinessService.processOrder()] completed successfully, duration: 52ms [TraceId: test-trace-001]
            
        } finally {
            MDC.clear();
        }
    }
    
    /**
     * 测试带注解的方法（记录参数和返回值）
     */
    @Test
    public void testAnnotatedMethodWithArgsAndResult() {
        MDC.put("traceId", "test-trace-002");
        
        try {
            String result = testBusinessService.createProduct("TestProduct", 10);
            
            assert result.contains("TestProduct");
            assert result.contains("10");
            
            // 日志输出示例：
            // TRACE [METHOD-TIME] Method [TestBusinessService.createProduct()] started with args: [TestProduct, 10] [TraceId: test-trace-002]
            // TRACE [METHOD-TIME] Method [TestBusinessService.createProduct()] completed successfully with result: Product TestProduct created with quantity 10, duration: 32ms [TraceId: test-trace-002]
            
        } finally {
            MDC.clear();
        }
    }
    
    /**
     * 测试慢方法告警
     */
    @Test
    public void testSlowMethodWarning() {
        MDC.put("traceId", "test-trace-003");
        
        try {
            String result = testBusinessService.slowOperation();
            
            assert result.contains("completed");
            
            // 日志输出示例：
            // TRACE [METHOD-TIME] Method [TestBusinessService.slowOperation()] started [TraceId: test-trace-003]
            // WARN [METHOD-TIME] [SLOW METHOD] Method [TestBusinessService.slowOperation()] completed successfully, duration: 102ms [TraceId: test-trace-003]
            
        } finally {
            MDC.clear();
        }
    }
    
    /**
     * 测试异常方法的处理
     */
    @Test
    public void testMethodWithException() {
        MDC.put("traceId", "test-trace-004");
        
        try {
            try {
                testBusinessService.methodWithException();
                assert false : "Should throw exception";
            } catch (RuntimeException e) {
                assert e.getMessage().contains("Business exception");
                
                // 日志输出示例：
                // TRACE [METHOD-TIME] Method [TestBusinessService.methodWithException()] started [TraceId: test-trace-004]
                // TRACE [METHOD-TIME] Method [TestBusinessService.methodWithException()] failed with exception: RuntimeException - Business exception for testing, duration: 2ms [TraceId: test-trace-004]
            }
        } finally {
            MDC.clear();
        }
    }
    
    /**
     * 测试排除的方法（getter/setter）
     */
    @Test
    public void testExcludedMethods() {
        // 这些方法不应该被拦截，不会有耗时日志
        String name = testBusinessService.getName();
        assert "TestService".equals(name);
        
        testBusinessService.setName("NewName");
        
        // 这些方法调用不会产生任何耗时统计日志
    }
    
    /**
     * 测试无TraceId的情况
     */
    @Test
    public void testWithoutTraceId() {
        // 确保没有TraceId
        MDC.clear();
        
        String result = testBusinessService.processOrder("ORDER-456");
        assert result.contains("ORDER-456");
        
        // 日志输出示例（没有TraceId部分）：
        // TRACE [METHOD-TIME] Method [TestBusinessService.processOrder()] started
        // TRACE [METHOD-TIME] Method [TestBusinessService.processOrder()] completed successfully, duration: 51ms
    }
    
    /**
     * 性能测试 - 验证AOP对性能的影响
     */
    @Test
    public void testPerformanceImpact() {
        int iterations = 1000;
        
        // 测试有AOP的情况
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            testBusinessService.processOrder("ORDER-" + i);
        }
        long withAopTime = System.currentTimeMillis() - startTime;
        
        System.out.println("Time with AOP: " + withAopTime + "ms for " + iterations + " iterations");
        
        // 在实际项目中，可以通过配置关闭AOP来对比性能差异
        // 验证AOP的性能影响在可接受范围内
    }
}
