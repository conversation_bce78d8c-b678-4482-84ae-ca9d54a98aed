package cn.need.cloud.biz.service.example;

import cn.need.framework.starter.web.annotation.MethodTimeLog;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.ArrayList;

/**
 * 方法耗时统计使用示例
 * 
 * 这个示例展示了如何在实际的Service类中使用方法耗时统计功能。
 * 包含了各种使用场景和最佳实践。
 * 
 * <AUTHOR>
 */
@Service
public class ExampleService {
    
    // ================================
    // 1. 自动拦截的方法（无需注解）
    // ================================
    
    /**
     * 普通业务方法 - 会被自动拦截并记录耗时
     * 
     * 日志输出示例：
     * TRACE [METHOD-TIME] Method [ExampleService.createOrder()] started [TraceId: abc123]
     * TRACE [METHOD-TIME] Method [ExampleService.createOrder()] completed successfully, duration: 45ms [TraceId: abc123]
     */
    public String createOrder(String customerId, List<String> productIds) {
        // 模拟业务处理
        try {
            Thread.sleep(50); // 模拟数据库操作
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        return "ORDER-" + System.currentTimeMillis();
    }
    
    /**
     * 查询方法 - 自动拦截
     */
    public List<String> queryOrders(String customerId) {
        // 模拟查询操作
        List<String> orders = new ArrayList<>();
        orders.add("ORDER-001");
        orders.add("ORDER-002");
        return orders;
    }
    
    // ================================
    // 2. 使用注解进行精确控制
    // ================================
    
    /**
     * 记录参数和返回值的方法
     * 
     * 日志输出示例：
     * TRACE [METHOD-TIME] Method [ExampleService.updateOrder()] started with args: [ORDER-123, SHIPPED] [TraceId: abc123]
     * TRACE [METHOD-TIME] Method [ExampleService.updateOrder()] completed successfully with result: Order updated successfully, duration: 120ms [TraceId: abc123]
     */
    @MethodTimeLog(logArgs = true, logResult = true, description = "更新订单状态")
    public String updateOrder(String orderId, String status) {
        // 模拟更新操作
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        return "Order updated successfully";
    }
    
    /**
     * 设置慢方法阈值的方法
     * 
     * 当执行时间超过500ms时会以WARN级别记录：
     * WARN [METHOD-TIME] [SLOW METHOD] Method [ExampleService.complexCalculation()] completed successfully, duration: 800ms [TraceId: abc123]
     */
    @MethodTimeLog(slowThreshold = 500L, description = "复杂计算")
    public double complexCalculation(int iterations) {
        double result = 0.0;
        
        // 模拟复杂计算
        for (int i = 0; i < iterations; i++) {
            result += Math.sqrt(i) * Math.sin(i);
            
            // 模拟耗时操作
            if (i % 1000 == 0) {
                try {
                    Thread.sleep(1);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        return result;
    }
    
    /**
     * 可能抛异常的方法
     * 
     * 异常情况的日志输出示例：
     * TRACE [METHOD-TIME] Method [ExampleService.riskyOperation()] started [TraceId: abc123]
     * TRACE [METHOD-TIME] Method [ExampleService.riskyOperation()] failed with exception: RuntimeException - Operation failed, duration: 15ms [TraceId: abc123]
     */
    @MethodTimeLog(logArgs = true)
    public String riskyOperation(String input) {
        if ("error".equals(input)) {
            throw new RuntimeException("Operation failed");
        }
        
        return "Success: " + input;
    }
    
    // ================================
    // 3. 类级别注解示例
    // ================================
    
    /**
     * 内部服务类 - 使用类级别注解
     * 类上的注解会应用到所有public方法
     */
    @MethodTimeLog(slowThreshold = 2000L, description = "数据处理服务")
    public static class DataProcessingService {
        
        /**
         * 继承类级别的配置
         */
        public void processData(String data) {
            // 数据处理逻辑
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        /**
         * 方法级别的注解会覆盖类级别的配置
         */
        @MethodTimeLog(logArgs = true, logResult = true, slowThreshold = 1000L)
        public String transformData(String input) {
            return "Transformed: " + input;
        }
    }
    
    // ================================
    // 4. 不会被拦截的方法
    // ================================
    
    /**
     * getter方法 - 不会被拦截
     */
    public String getName() {
        return "ExampleService";
    }
    
    /**
     * setter方法 - 不会被拦截
     */
    public void setName(String name) {
        // setter logic
    }
    
    /**
     * 布尔判断方法 - 不会被拦截
     */
    public boolean isActive() {
        return true;
    }
    
    /**
     * Object基础方法 - 不会被拦截
     */
    @Override
    public String toString() {
        return "ExampleService{}";
    }
    
    // ================================
    // 5. 实际业务场景示例
    // ================================
    
    /**
     * 产品创建 - 记录详细信息用于审计
     */
    @MethodTimeLog(logArgs = true, logResult = true, slowThreshold = 2000L, description = "创建产品")
    public Long createProduct(String productName, String category, Double price) {
        // 模拟产品创建流程
        try {
            // 1. 验证参数
            Thread.sleep(10);
            
            // 2. 检查重复
            Thread.sleep(50);
            
            // 3. 保存到数据库
            Thread.sleep(100);
            
            // 4. 更新缓存
            Thread.sleep(20);
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        return System.currentTimeMillis();
    }
    
    /**
     * 批量处理 - 设置较高的慢方法阈值
     */
    @MethodTimeLog(slowThreshold = 5000L, description = "批量处理订单")
    public int batchProcessOrders(List<String> orderIds) {
        int processedCount = 0;
        
        for (String orderId : orderIds) {
            try {
                // 模拟处理每个订单
                Thread.sleep(10);
                processedCount++;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        return processedCount;
    }
    
    /**
     * 数据库查询 - 监控查询性能
     */
    @MethodTimeLog(slowThreshold = 1000L, description = "复杂查询")
    public List<String> complexQuery(String condition, int limit) {
        List<String> results = new ArrayList<>();
        
        // 模拟复杂查询
        try {
            Thread.sleep(200); // 模拟数据库查询时间
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 模拟结果
        for (int i = 0; i < limit; i++) {
            results.add("Result-" + i);
        }
        
        return results;
    }
    
    /**
     * 外部API调用 - 监控外部依赖性能
     */
    @MethodTimeLog(logArgs = true, slowThreshold = 3000L, description = "调用外部API")
    public String callExternalApi(String endpoint, String payload) {
        // 模拟外部API调用
        try {
            Thread.sleep(500); // 模拟网络延迟
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        return "API Response";
    }
}
