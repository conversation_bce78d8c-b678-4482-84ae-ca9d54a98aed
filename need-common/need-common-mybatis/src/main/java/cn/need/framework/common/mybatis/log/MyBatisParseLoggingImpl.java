package cn.need.framework.common.mybatis.log;

import cn.need.framework.common.core.lang.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.time.LocalDateTime;
import java.util.*;

@Slf4j
public class MyBatisParseLoggingImpl implements org.apache.ibatis.logging.Log {

    private static final ThreadLocal<Map<String, Object>> sqlContext = ThreadLocal.withInitial(HashMap::new);


    public MyBatisParseLoggingImpl(String clazz) {
    }

    @Override
    public boolean isDebugEnabled() {
        return log.isDebugEnabled();
    }

    @Override
    public boolean isTraceEnabled() {
        return log.isTraceEnabled();
    }

    @Override
    public void error(String s, Throwable e) {
        log.error(s, e);
    }

    @Override
    public void error(String s) {
        log.error(s);
    }

    @Override
    public void debug(String s) {

        // 处理 SQL 日志
        try {
            if (s.startsWith("==>  Preparing:")) {
                // 存储 SQL 语句
                String sql = s.substring("==>  Preparing:".length()).trim();
                storeSql(sql);
            } else if (s.startsWith("==> Parameters:")) {
                // 存储参数
                String parameters = s.substring("==> Parameters:".length()).trim();
                storeParameter(parameters);
                // 触发 SQL 替换并打印
                replaceAndPrintSql();
            }
        } catch (Exception exception) {
            // 打印原生的 SQL
            log.error(s);
            log.error("Error while parsing SQL", exception);
        }
    }

    @Override
    public void trace(String s) {
        log.trace(s);
    }

    @Override
    public void warn(String s) {
        log.warn(s);
    }

    private void storeSql(String sql) {
        // 清理 ThreadLocal
        Map<String, Object> context = sqlContext.get();
        context.put("sql", sql);
        // 记录SQL开始执行时间
        context.put("startTime", System.currentTimeMillis());
    }

    private void storeParameter(String parameterLine) {
        Map<String, Object> context = sqlContext.get();
        if (StringUtil.isBlank(parameterLine)) {
            return;
        }
        // 按逗号分隔多个参数
        String[] parameterParts = parameterLine.split(",");

        // 存储处理后的参数值
        String[] parameters = new String[parameterParts.length];
        for (int i = 0; i < parameterParts.length; i++) {
            String part = parameterParts[i].trim();

            // 提取参数值和类型（例如 "WH01C01G-OCW-2503130001(String)"）
            int typeStartIndex = part.lastIndexOf("(");
            int typeEndIndex = part.lastIndexOf(")");

            // 检查索引是否有效
            if (typeStartIndex == -1 || typeEndIndex == -1 || typeStartIndex >= typeEndIndex) {
                // 如果格式不正确，直接使用原始值
                parameters[i] = part;
                continue;
            }

            String value = part.substring(0, typeStartIndex).trim();
            String type = part.substring(typeStartIndex + 1, typeEndIndex).trim();

            List<String> strTypeList = Arrays.asList(
                    String.class.getSimpleName(),
                    LocalDateTime.class.getSimpleName(),
                    Date.class.getSimpleName()
            );

            // 如果是字符类型，则添加单引号
            if (strTypeList.contains(type)) {
                value = "'" + value + "'";
            }

            parameters[i] = value;
        }

        // 将处理后的参数存储到 ThreadLocal 中
        context.put("parameters", parameters);
    }


    private void replaceAndPrintSql() {
        Map<String, Object> context = sqlContext.get();
        String sql = (String) context.get("sql");
        String[] parameters = (String[]) context.get("parameters");
        Long startTime = (Long) context.get("startTime");

        if (sql == null) {
            return;
        }

        // 计算SQL执行耗时
        long duration = 0;
        if (startTime != null) {
            duration = System.currentTimeMillis() - startTime;
        }

        StringBuilder replacedSql = new StringBuilder(sql);
        if (parameters != null) {
            // 逐个替换 SQL 中的占位符

            int parameterIndex = 0;
            int startIndex = 0;

            while (startIndex < replacedSql.length()) {
                int questionMarkIndex = replacedSql.indexOf("?", startIndex);
                if (questionMarkIndex == -1) {
                    break;
                }

                if (parameterIndex >= parameters.length) {
                    log.warn("More placeholders than parameters in SQL: {}", sql);
                    break;
                }

                String parameter = parameters[parameterIndex];
                replacedSql.replace(questionMarkIndex, questionMarkIndex + 1, parameter);
                startIndex = questionMarkIndex + parameter.length();
                parameterIndex++;
            }

            if (parameterIndex < parameters.length) {
                log.warn("More parameters than placeholders in SQL: {}", sql);
            }
        }

        // 获取链路追踪ID
        String traceId = MDC.get("traceId");
        String traceInfo = StringUtil.isNotBlank(traceId) ? " [TraceId: " + traceId + "]" : "";

        log.debug("==> Replace SQL: \n{}\n{}\n执行耗时: {}ms\n{}",
                "<================================  执行SQL   ================================>" + traceInfo,
                replacedSql,
                duration,
                "<================================  执行SQL   ================================>" + traceInfo);
        // 清理 ThreadLocal
        sqlContext.remove();
    }
}