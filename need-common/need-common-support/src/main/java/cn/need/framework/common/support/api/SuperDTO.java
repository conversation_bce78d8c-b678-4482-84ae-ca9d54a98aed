package cn.need.framework.common.support.api;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class SuperDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -8939858807419302139L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 最后更新人
     */
    private Long updateBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;

}
